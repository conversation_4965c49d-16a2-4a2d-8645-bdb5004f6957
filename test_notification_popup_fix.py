#!/usr/bin/env python3
"""
通知弹窗处理修复验证脚本
测试Kaipoke系统通知弹窗的检测和关闭功能
"""

import sys
import os

def check_notification_popup_detection():
    """检查通知弹窗检测功能"""
    print("🔍 检查通知弹窗检测功能...")
    
    popup_engine_file = "core/popup_handler/popup_engine.py"
    
    if not os.path.exists(popup_engine_file):
        print(f"❌ 文件不存在: {popup_engine_file}")
        return False
    
    with open(popup_engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查增强的检测功能
    detection_checks = [
        ("检测并关闭通知类弹窗（增强版）", "增强版检测函数"),
        ("首先检查是否有可见的模态框", "模态框检查"),
        ("检测到.*个可见模态框", "模态框计数"),
        ("确认第.*个模态框是通知弹窗", "通知弹窗确认"),
        ("サテライト", "新增关键词检测"),
        ("_close_specific_notification_modal", "专用关闭函数"),
        ("通过按钮关闭", "按钮关闭逻辑"),
        ("通过背景关闭", "背景点击关闭"),
        ("强制隐藏模态框", "强制隐藏逻辑")
    ]
    
    all_passed = True
    for check_text, description in detection_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def check_tennki_popup_handling():
    """检查tennki工作流中的弹窗处理"""
    print("\n🔧 检查tennki工作流弹窗处理...")
    
    tennki_file = "core/rpa_tools/tennki_form_engine.py"
    
    with open(tennki_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查tennki中的弹窗处理增强
    tennki_checks = [
        ("优先检测和清理通知弹窗（增强版）", "增强版清理逻辑"),
        ("多次尝试清理通知弹窗", "多次尝试机制"),
        ("通知弹窗清理尝试", "尝试计数"),
        ("确认所有通知弹窗已关闭", "关闭确认"),
        ("仍有.*个模态框可见", "残留检测"),
        ("_force_close_all_modals", "强制关闭函数"),
        ("强制关闭所有可见模态框", "强制关闭逻辑"),
        ("点击关闭按钮成功", "按钮点击成功"),
        ("强制隐藏模态框", "强制隐藏"),
        ("移除背景遮罩", "背景清理")
    ]
    
    all_passed = True
    for check_text, description in tennki_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def check_close_button_selectors():
    """检查关闭按钮选择器"""
    print("\n🎯 检查关闭按钮选择器...")
    
    popup_engine_file = "core/popup_handler/popup_engine.py"
    
    with open(popup_engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关闭按钮选择器
    selector_checks = [
        ("button:contains(\"×\")", "×按钮选择器"),
        ("button:contains(\"✕\")", "✕按钮选择器"),
        ("button:contains(\"お知らせはこちら\")", "通知按钮选择器"),
        (".modal-header .close", "模态框头部关闭按钮"),
        (".modal-footer button", "模态框底部按钮"),
        ("button[aria-label=\"Close\"]", "ARIA标签关闭按钮"),
        (".modal-backdrop", "背景遮罩点击"),
        ("modal.classList.remove('in', 'show')", "CSS类移除"),
        ("document.body.classList.remove('modal-open')", "body类清理")
    ]
    
    all_passed = True
    for check_text, description in selector_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def show_notification_popup_solution():
    """显示通知弹窗解决方案"""
    print("\n📋 通知弹窗处理解决方案")
    print("=" * 50)
    
    print("🔍 问题分析:")
    print("   - 用户截图显示: 訪問看護出張所料金体系通知弹窗")
    print("   - 问题现象: 弹窗未关闭就点击新規追加按钮")
    print("   - 根本原因: 通知弹窗阻挡了新規追加按钮")
    
    print("\n🔧 解决策略:")
    print("1. 增强通知弹窗检测")
    print("   - 检测可见模态框数量")
    print("   - 分析模态框内容关键词")
    print("   - 确认是否为通知类弹窗")
    
    print("\n2. 多种关闭方式")
    print("   - 查找并点击关闭按钮 (×, ✕, お知らせはこちら)")
    print("   - 点击模态框背景区域")
    print("   - JavaScript强制隐藏")
    
    print("\n3. 多次尝试机制")
    print("   - 最多3次清理尝试")
    print("   - 每次尝试后验证是否关闭")
    print("   - 强制关闭所有残留模态框")
    
    print("\n4. 完整的清理流程")
    print("   - 移除模态框CSS类")
    print("   - 清理背景遮罩")
    print("   - 恢复body样式")

def generate_fix_summary():
    """生成修复总结"""
    print("\n📊 通知弹窗处理修复验证")
    print("=" * 50)
    
    detection_check = check_notification_popup_detection()
    tennki_check = check_tennki_popup_handling()
    selector_check = check_close_button_selectors()
    
    print(f"\n📈 检查结果:")
    print(f"   - 弹窗检测: {'✅ 通过' if detection_check else '❌ 失败'}")
    print(f"   - Tennki处理: {'✅ 通过' if tennki_check else '❌ 失败'}")
    print(f"   - 关闭选择器: {'✅ 通过' if selector_check else '❌ 失败'}")
    
    if detection_check and tennki_check and selector_check:
        print("\n🎉 通知弹窗处理修复已正确实施！")
        print("\n📝 预期效果:")
        print("   1. 自动检测并关闭通知弹窗")
        print("   2. 确保新規追加按钮可正常点击")
        print("   3. 多重保障机制防止弹窗阻挡")
        print("   4. 详细的处理过程日志")
        
        print("\n🚀 解决用户截图中的问题:")
        print("   1. 检测到訪問看護出張所通知弹窗")
        print("   2. 自动点击×按钮或お知らせはこちら按钮")
        print("   3. 确认弹窗完全关闭后再点击新規追加")
        print("   4. 避免弹窗阻挡导致的操作失败")
        
        print("\n📋 下一步测试建议:")
        print("   1. 运行kaipoke_tennki工作流")
        print("   2. 观察通知弹窗的检测和关闭过程")
        print("   3. 确认新規追加按钮能正常点击")
        print("   4. 检查是否还有其他类型的弹窗")
        return True
    else:
        print("\n⚠️ 部分修复可能未正确实施，请检查代码")
        return False

if __name__ == "__main__":
    print("🔍 通知弹窗处理修复验证")
    print("=" * 60)
    
    # 显示解决方案
    show_notification_popup_solution()
    
    # 执行检查
    success = generate_fix_summary()
    
    if success:
        print(f"\n✅ 通知弹窗处理修复验证完成 - 所有检查通过")
        print(f"\n💡 这应该能解决用户截图中的通知弹窗问题！")
        sys.exit(0)
    else:
        print(f"\n❌ 通知弹窗处理修复验证失败 - 请检查实施")
        sys.exit(1)
