#!/usr/bin/env python3
"""
Kaipoke Tennki 弹窗关闭修复测试脚本
测试使用 ._icon-close__bF1y_ 选择器关闭弹窗后再点击新規追加
"""

import asyncio
import logging
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_popup_close_and_add_button():
    """测试弹窗关闭和新規追加按钮点击"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            logger.info("🚀 开始测试弹窗关闭和新規追加按钮")
            
            # 导航到测试页面
            logger.info("📍 导航到Kaipoke页面...")
            await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 测试1: 检测弹窗关闭按钮
            logger.info("🔍 测试1: 检测弹窗关闭按钮")
            
            close_selectors = [
                '._icon-close__bF1y_',  # 用户指定的关闭选择器
                '.close',
                '.modal-close',
                'button[aria-label="Close"]',
                '.btn-close'
            ]
            
            found_close_buttons = []
            for selector in close_selectors:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        is_visible = await page.locator(selector).is_visible()
                        if is_visible:
                            found_close_buttons.append(selector)
                            logger.info(f"✅ 找到可见的关闭按钮: {selector}")
                        else:
                            logger.debug(f"🔍 找到但不可见的关闭按钮: {selector}")
                    else:
                        logger.debug(f"❌ 未找到关闭按钮: {selector}")
                except Exception as e:
                    logger.debug(f"⚠️ 检测关闭按钮失败: {selector}, {e}")
            
            # 测试2: 如果有弹窗，尝试关闭
            if found_close_buttons:
                logger.info("🔍 测试2: 尝试关闭弹窗")
                
                for selector in found_close_buttons:
                    try:
                        await page.click(selector, timeout=3000)
                        logger.info(f"✅ 成功点击关闭按钮: {selector}")
                        await page.wait_for_timeout(2000)  # 等待弹窗关闭
                        break
                    except Exception as e:
                        logger.warning(f"⚠️ 关闭按钮点击失败: {selector}, {e}")
                        continue
            else:
                logger.info("🔍 测试2: 未发现弹窗，跳过关闭步骤")
            
            # 测试3: 检查新規追加按钮
            logger.info("🔍 测试3: 检查新規追加按钮")
            
            add_button_selector = '#btn_area .cf:nth-child(1) :nth-child(1)'
            button_count = await page.locator(add_button_selector).count()
            
            if button_count > 0:
                logger.info(f"✅ 找到新規追加按钮: {button_count} 个")
                
                # 测试4: 尝试点击新規追加按钮
                logger.info("🔍 测试4: 尝试点击新規追加按钮")
                
                try:
                    await page.click(add_button_selector, timeout=5000)
                    logger.info("✅ 新規追加按钮点击成功")
                    
                    # 等待数据登录表单出现
                    await page.wait_for_timeout(3000)
                    
                    # 检查数据登录表单是否出现
                    kaigo_visible = await page.locator('#inPopupInsuranceDivision01').is_visible()
                    iryou_visible = await page.locator('#inPopupInsuranceDivision02').is_visible()
                    
                    if kaigo_visible or iryou_visible:
                        logger.info("✅ 数据登录表单成功出现")
                        
                        # 等待5秒确保表单不会消失
                        logger.info("🔍 测试5: 等待5秒确保表单稳定")
                        await page.wait_for_timeout(5000)
                        
                        kaigo_still_visible = await page.locator('#inPopupInsuranceDivision01').is_visible()
                        iryou_still_visible = await page.locator('#inPopupInsuranceDivision02').is_visible()
                        
                        if kaigo_still_visible or iryou_still_visible:
                            logger.info("✅ 表单保持稳定，修复成功！")
                            return True
                        else:
                            logger.error("❌ 表单在5秒后消失了")
                            return False
                    else:
                        logger.error("❌ 数据登录表单未出现")
                        return False
                        
                except Exception as e:
                    logger.error(f"❌ 新規追加按钮点击失败: {e}")
                    
                    # 检查是否是Karte组件阻挡
                    if "karte" in str(e).lower():
                        logger.warning("⚠️ 检测到Karte组件阻挡，这需要进一步处理")
                    
                    return False
            else:
                logger.warning("⚠️ 未找到新規追加按钮，可能需要登录或导航到正确页面")
                return False
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
            
        finally:
            await browser.close()

async def main():
    """主函数"""
    logger.info("🧪 Kaipoke Tennki 弹窗关闭和新規追加按钮测试")
    
    success = await test_popup_close_and_add_button()
    
    if success:
        logger.info("🎉 测试成功！弹窗关闭和表单保护机制正常工作")
    else:
        logger.error("💥 测试失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
