# Kaipoke Tennki 简化工作流程说明

**更新时间**: 2025-07-24  
**目标**: 简化月間スケジュール一覧页面的处理逻辑，回归到基本的通知弹窗检测和新規追加操作

## 🎯 简化目标

根据用户反馈，当前工作流程过于复杂，存在以下问题：
1. 有通知窗口时没有点击关闭按钮就一直点击新規追加
2. 根据保险种类显示的选项不同，可能有强制处理导致页面改变
3. 需要回归到简单的处理逻辑

## 🔄 简化后的工作流程

### 第一步：进入月間スケジュール一覧页面
- 导航到目标据点的月間スケジュール一覧页面
- 等待页面完全加载

### 第二步：检测通知弹窗
```python
async def _close_oshirase_notification_only(self, page):
    """简化的通知弹窗检测和关闭（月間スケジュール一覧页面专用）"""
    
    # 检测常见的通知弹窗选择器
    notification_selectors = [
        '._icon-close__bF1y_',  # 主要的お知らせ关闭按钮
        '.modal .close',        # 模态框关闭按钮
        '.notification .close', # 通知关闭按钮
        'button:has-text("×")', # X关闭按钮
        'button:has-text("閉じる")', # 关闭按钮
        '.popup .close'         # 弹窗关闭按钮
    ]
    
    # 如果发现通知弹窗，点击关闭
    # 如果没有发现，直接进行下一步
```

### 第三步：点击新規追加按钮
```python
async def _click_add_button(self):
    """简化的新規追加按钮点击（月間スケジュール一覧页面专用）"""
    
    # 1. 检查是否已有数据登录窗口打开
    # 2. 如果没有，点击新規追加按钮
    # 3. 等待数据登录表单出现
    # 4. 激活保险种类选择器
```

### 第四步：等待保险种类选择器加载
```python
async def _safe_activate_form_fields(self, page):
    """简化的保险种类选择器激活和等待"""
    
    # 1. 等待保险种类选择器出现
    # 2. 激活所有可用的保险类型选择器
    # 3. 确认选择器已准备就绪
```

## 🔧 关键改进点

### 1. 简化通知弹窗检测
- **之前**: 复杂的弹窗处理引擎，多层检测逻辑
- **现在**: 简单的选择器检测，发现就关闭，没有就跳过

### 2. 直接的新規追加点击
- **之前**: 多种备用方法，复杂的重试逻辑
- **现在**: 优先使用精确选择器 `#btn_area .cf:nth-child(1) :nth-child(1)`

### 3. 专注的保险种类选择器等待
- **之前**: 激活所有表单字段，触发各种事件
- **现在**: 只关注保险种类选择器的加载和激活

### 4. 移除强制处理逻辑
- **之前**: 页面刷新、强制清理、复杂的状态检测
- **现在**: 简单的状态检查，自然的流程进行

## 📋 执行顺序

1. **进入月間スケジュール一覧页面** ✅
2. **检测通知弹窗** 
   - 有 → 点击关闭 → 继续
   - 无 → 直接继续
3. **点击新規追加按钮**
   - 使用精确选择器点击
   - 等待数据登录表单出现
4. **等待保险种类选择器加载**
   - 等待选择器出现
   - 激活选择器
   - 确认可以选择
5. **根据保险种类进行后续处理** ✅

## 🎯 预期效果

- **减少错误**: 简化逻辑减少出错概率
- **提高稳定性**: 移除强制处理，让页面自然加载
- **更好的用户体验**: 清晰的流程，明确的状态反馈
- **易于维护**: 简单的代码结构，容易理解和修改

## 🔍 调试信息

工作流程会输出以下关键日志：
- `🔍 检测月間スケジュール一覧页面通知弹窗...`
- `🔍 发现通知弹窗，点击关闭` 或 `🔍 未发现通知弹窗，可以直接进行新規追加操作`
- `🔘 点击新規追加按钮...`
- `✅ 数据登录表单已出现`
- `🔧 等待保险种类选择器加载...`
- `✅ 保险种类选择器已准备就绪，可以进行选择`

这样用户可以清楚地看到每一步的执行状态。
