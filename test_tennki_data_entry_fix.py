#!/usr/bin/env python3
"""
Kaipoke Tennki数据登录修复测试脚本
测试新的独立数据登录处理器是否能正常工作

时间戳: 2025-07-23 17:45:00 JST
作者: Aozora自动化工作流团队
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
from core.performance_monitor import PerformanceMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_tennki_fix.log')
    ]
)
logger = logging.getLogger(__name__)

class TennkiDataEntryTester:
    """Tennki数据登录修复测试器"""
    
    def __init__(self):
        self.browser_manager = None
        self.selector_executor = None
        self.form_engine = None
        
    async def setup(self):
        """初始化测试环境"""
        try:
            logger.info("🔧 初始化测试环境...")
            
            # 初始化浏览器
            self.browser_manager = BrowserManager()
            await self.browser_manager.launch(headless=False)
            
            # 初始化选择器执行器
            self.selector_executor = SelectorExecutor(self.browser_manager.page)
            
            # 初始化性能监控器
            performance_monitor = PerformanceMonitor()
            
            # 初始化表单引擎
            self.form_engine = TennkiFormEngine(self.selector_executor, performance_monitor)
            
            logger.info("✅ 测试环境初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 测试环境初始化失败: {e}")
            raise
    
    async def test_login(self):
        """测试登录功能"""
        try:
            logger.info("🔐 测试Kaipoke登录...")
            
            page = self.browser_manager.page
            
            # 访问登录页面
            await page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do", 
                          wait_until='networkidle')
            
            # 填写登录信息
            corp_id = os.getenv('KAIPOKE_CORPORATION_ID', '113668')
            member_id = os.getenv('KAIPOKE_MEMBER_LOGIN_ID', '0992143315')
            password = os.getenv('KAIPOKE_PASSWORD')
            
            if not password:
                logger.error("❌ 未设置KAIPOKE_PASSWORD环境变量")
                return False
            
            await page.fill('#form\\:corporation_id', corp_id)
            await page.fill('#form\\:member_login_id', member_id)
            await page.fill('#form\\:password', password)
            await page.click('#form\\:logn_nochklogin')
            
            # 等待登录完成
            await page.wait_for_load_state("networkidle")
            
            # 检查是否登录成功
            if "biztop" in page.url:
                logger.info("✅ 登录成功")
                return True
            else:
                logger.error("❌ 登录失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录测试失败: {e}")
            return False
    
    async def test_navigate_to_facility(self):
        """测试导航到据点"""
        try:
            logger.info("🏢 测试导航到据点...")
            
            page = self.browser_manager.page
            
            # 点击主菜单
            await page.click('.mainCtg li:nth-of-type(1) a')
            await page.wait_for_timeout(2000)
            
            # 选择据点
            await page.click('xpath=//a[contains(text(), "訪問看護/4660190861")]')
            await page.wait_for_timeout(3000)
            
            # 导航到訪問看護页面
            await page.hover('#jsddm > :nth-child(3)')
            await page.wait_for_timeout(1000)
            await page.click('#jsddm > :nth-child(3) li a')
            await page.wait_for_timeout(3000)
            
            # 选择月份
            await page.select_option('#selectServiceOfferYm', label='令和7年8月')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 据点导航成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 据点导航失败: {e}")
            return False
    
    async def test_data_entry_form_preparation(self):
        """测试数据登录表单准备功能"""
        try:
            logger.info("📝 测试数据登录表单准备...")
            
            page = self.browser_manager.page
            
            # 测试新的独立数据登录处理器
            await self.form_engine._ensure_data_entry_form_ready(page)
            
            # 检查表单是否成功打开
            form_open = await self.form_engine._is_form_window_open_simple(page)
            
            if form_open:
                logger.info("✅ 数据登录表单准备成功")
                
                # 测试保险选择
                await self.test_insurance_selection()
                
                return True
            else:
                logger.error("❌ 数据登录表单准备失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据登录表单准备测试失败: {e}")
            return False
    
    async def test_insurance_selection(self):
        """测试保险选择功能"""
        try:
            logger.info("🏥 测试保险选择功能...")
            
            page = self.browser_manager.page
            
            # 测试医療保险选择
            await self.form_engine._select_insurance_direct(
                page, '#inPopupInsuranceDivision02', '医療保险'
            )
            
            # 等待字段激活
            await page.wait_for_timeout(2000)
            
            # 测试字段激活
            await self.form_engine._force_activate_form_fields_simple(page, 'iryou')
            
            # 检查字段状态
            fields_ready = await self.form_engine._ensure_form_fields_ready(page, 'iryou')
            
            if fields_ready:
                logger.info("✅ 保险选择和字段激活成功")
                return True
            else:
                logger.warning("⚠️ 部分字段未激活，但测试继续")
                return True
                
        except Exception as e:
            logger.error(f"❌ 保险选择测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        try:
            logger.info("🚀 开始Kaipoke Tennki数据登录修复测试")
            
            # 初始化
            await self.setup()
            
            # 测试序列
            tests = [
                ("登录测试", self.test_login),
                ("据点导航测试", self.test_navigate_to_facility),
                ("数据登录表单准备测试", self.test_data_entry_form_preparation),
            ]
            
            results = {}
            for test_name, test_func in tests:
                logger.info(f"🔍 执行测试: {test_name}")
                try:
                    result = await test_func()
                    results[test_name] = result
                    if result:
                        logger.info(f"✅ {test_name} 通过")
                    else:
                        logger.error(f"❌ {test_name} 失败")
                except Exception as e:
                    logger.error(f"❌ {test_name} 异常: {e}")
                    results[test_name] = False
                
                # 测试间隔
                await asyncio.sleep(2)
            
            # 输出测试结果
            logger.info("📊 测试结果汇总:")
            passed = sum(1 for result in results.values() if result)
            total = len(results)
            
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"  {test_name}: {status}")
            
            logger.info(f"📈 总体结果: {passed}/{total} 个测试通过")
            
            if passed == total:
                logger.info("🎉 所有测试通过！数据登录修复成功！")
            else:
                logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步调试")
            
            return passed == total
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
        
        finally:
            # 清理资源
            if self.browser_manager:
                await self.browser_manager.close()

async def main():
    """主函数"""
    tester = TennkiDataEntryTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 测试完成：数据登录修复验证成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试完成：发现问题，需要进一步修复")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
