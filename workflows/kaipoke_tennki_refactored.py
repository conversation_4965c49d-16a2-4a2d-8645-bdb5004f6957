"""
Kaipoke Tennki Workflow (全面重构版 - MCP強化)
基于方案二：全面重构，充分利用现有模块

核心优化：
1. 登录会话复用：使用KaipokeLoginService，减少登录时间95%
2. 批量数据预处理：一次性读取Google Sheets，按用户分组
3. 智能表单填写引擎：优化等待时间，支持并发处理
4. MCP三层备份机制：确保选择器稳定性
5. 高效批量写入：智能批次大小调整

性能目标：67小时 → 4-6小时（90%+提升）

🆕 架构设计：
- TennkiWorkflowManager: 主控制器
- TennkiDataProcessor: 数据处理引擎
- TennkiFacilityManager: 据点管理
- TennkiFormEngine: 表单填写引擎
- TennkiPerformanceMonitor: 性能监控
"""

import asyncio
import os
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.gsuite.sheets_client import SheetsClient
from core.rpa_tools.data_processor import DataProcessor
from core.rpa_tools.tennki_data_processor import TennkiDataProcessor
from core.rpa_tools.tennki_form_engine import TennkiFormEngine, TennkiConcurrentProcessor, TennkiSmartCache
# 🆕 移除弹窗处理引擎导入，完全保护数据登录表单
# from core.popup_handler.popup_decorator import handle_popups, kaipoke_safe, PopupContext
# from core.popup_handler.popup_engine import popup_engine


class TennkiWorkflowManager:
    """Tennki工作流主控制器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.performance_monitor = TennkiPerformanceMonitor()
        self.facility_manager = None
        self.form_engine = None
        self.concurrent_processor = None
        self.smart_cache = TennkiSmartCache()
        
    async def execute_workflow(self):
        """执行完整的Tennki工作流"""
        start_time = time.time()
        logger.info("🚀 开始执行Kaipoke Tennki全面重构工作流")
        
        try:
            # 1. 初始化所有组件
            await self._initialize_components()
            
            # 2. 按据点处理数据（每个据点使用独立的spreadsheet_id）
            for facility_config in self.config.get('facilities', []):
                await self._process_facility(facility_config)
            
            # 3. 性能报告
            execution_time = time.time() - start_time
            self.performance_monitor.log_final_report(execution_time)
            
            logger.info("✅ Kaipoke Tennki全面重构工作流完成")
            
        except Exception as e:
            logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)
            raise
    
    async def _initialize_components(self):
        """初始化所有组件"""
        logger.info("🔧 初始化工作流组件...")
        
        # 启动浏览器
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        
        # 初始化MCP备份工具
        await selector_executor.initialize_mcp_fallback()
        
        # 登录Kaipoke
        login_success = await kaipoke_login_with_env(
            page,
            self.config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
            self.config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
            self.config.get('password_env', 'KAIPOKE_PASSWORD'),
            self.config.get('login_url')
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
        
        # 初始化各个组件（不再初始化全局SheetsClient，每个据点单独初始化）
        self.facility_manager = TennkiFacilityManager(selector_executor)
        self.form_engine = TennkiFormEngine(selector_executor, self.performance_monitor)

        # 初始化并发处理器
        max_concurrent = self.config.get('performance_config', {}).get('max_concurrent_users', 3)
        self.concurrent_processor = TennkiConcurrentProcessor(self.form_engine, max_concurrent)
        
        logger.info("✅ 所有组件初始化完成")
    
    async def _process_facility(self, facility_config: dict):
        """处理单个据点的数据"""
        facility_name = facility_config.get('name')
        element_text = facility_config.get('element_text')
        facility_spreadsheet_id = facility_config.get('spreadsheet_id')

        logger.info(f"🏢 开始处理据点: {facility_name}")
        logger.info(f"📊 使用Spreadsheet ID: {facility_spreadsheet_id}")

        try:
            # 1. 为当前据点初始化专用的SheetsClient
            sheets_client = SheetsClient()
            sheets_client.spreadsheet_id = facility_spreadsheet_id

            # 2. 为当前据点初始化数据处理器
            data_processor = TennkiDataProcessor(
                sheets_client, self.config.get('sheet_name', '看護記録')
            )

            # 3. 批量数据预处理（针对当前据点的spreadsheet）
            processed_data = await data_processor.preprocess_all_data()
            logger.info(f"📊 据点 {facility_name} 数据预处理完成: {len(processed_data)} 条记录")

            # 4. 导航到据点
            await self.facility_manager.navigate_to_facility(element_text, facility_name)

            # 5. 导航到訪問看護页面
            await self.facility_manager.navigate_to_nursing_page()

            # 6. 智能批量处理数据（支持并发）
            if self.config.get('performance_config', {}).get('enable_concurrent', True):
                await self.concurrent_processor.process_users_concurrently(processed_data, facility_config)
            else:
                await self.form_engine.process_batch_data(processed_data, facility_config)

            logger.info(f"✅ 据点 {facility_name} 处理完成")

        except Exception as e:
            logger.error(f"❌ 据点 {facility_name} 处理失败: {e}")
            raise


class TennkiDataProcessor:
    """Tennki数据处理引擎"""
    
    def __init__(self, sheets_client: SheetsClient, sheet_name: str):
        self.sheets_client = sheets_client
        self.sheet_name = sheet_name
        
    async def preprocess_all_data(self) -> List[Dict]:
        """批量预处理所有数据"""
        logger.info("📊 开始批量数据预处理...")
        
        # 1. 一次性读取所有数据
        raw_data = self.sheets_client.read_sheet(
            self.sheets_client.spreadsheet_id,
            f'{self.sheet_name}!A2:AK'
        )
        
        if not raw_data:
            logger.warning("⚠️ 未读取到数据")
            return []
        
        logger.info(f"📋 读取原始数据: {len(raw_data)} 行")
        
        # 2. 数据验证和清洗
        valid_data = []
        for idx, row in enumerate(raw_data, start=2):
            if len(row) > 24:  # 确保有足够的列
                valid_data.append({
                    'row_index': idx,
                    'raw_data': row,
                    'user_name': row[24] if len(row) > 24 else '',
                    'insurance_type': row[19] if len(row) > 19 else '',
                    'processed': False
                })
        
        logger.info(f"✅ 有效数据: {len(valid_data)} 条")
        
        # 3. 按用户分组（减少重复用户选择）
        grouped_data = self._group_by_user(valid_data)
        logger.info(f"👥 用户分组: {len(grouped_data)} 个用户")
        
        # 4. 按保险种别预分类
        classified_data = self._classify_by_insurance(grouped_data)
        logger.info("🏥 保险种别分类完成")
        
        return classified_data
    
    def _group_by_user(self, data: List[Dict]) -> Dict[str, List[Dict]]:
        """按用户名分组数据"""
        grouped = {}
        for item in data:
            user_name = item['user_name']
            if user_name not in grouped:
                grouped[user_name] = []
            grouped[user_name].append(item)
        return grouped
    
    def _classify_by_insurance(self, grouped_data: Dict) -> List[Dict]:
        """按保险种别分类数据"""
        classified = []
        for user_name, user_data in grouped_data.items():
            # 按保险种别分组
            insurance_groups = {}
            for item in user_data:
                insurance_type = item['insurance_type']
                if insurance_type not in insurance_groups:
                    insurance_groups[insurance_type] = []
                insurance_groups[insurance_type].append(item)
            
            classified.append({
                'user_name': user_name,
                'insurance_groups': insurance_groups,
                'total_records': len(user_data)
            })
        
        return classified


class TennkiFacilityManager:
    """Tennki据点管理器"""
    
    def __init__(self, selector_executor: SelectorExecutor):
        self.selector_executor = selector_executor
        
    # 🆕 移除弹窗处理装饰器，保护数据登录表单
    async def navigate_to_facility(self, element_text: str, facility_name: str):
        """导航到指定据点"""
        logger.info(f"🏢 导航到据点: {facility_name}")

        page = self.selector_executor.page

        # 1. 点击主菜单
        await self.selector_executor.smart_click(
            workflow="kaipoke_tennki",
            category="navigation",
            element="main_menu",
            target_text="レセプト"
        )

        await page.wait_for_load_state("load")

        # 2. 选择据点
        success = await self.selector_executor.smart_click(
            workflow="kaipoke_tennki",
            category="navigation",
            element="facility_selection",
            target_text=element_text
        )

        if not success:
            # MCP备份
            await page.click(f'text="{element_text}"')

        await page.wait_for_load_state("load")

        logger.info(f"✅ 成功进入据点: {facility_name}")
    
    async def navigate_to_nursing_page(self):
        """导航到訪問看護页面"""
        logger.info("🏥 导航到訪問看護页面")

        page = self.selector_executor.page

        # 🆕 先处理Karte组件，避免阻挡交互
        await self._handle_karte_component(page)

        # 🆕 使用JavaScript直接点击，避免hover问题
        try:
            # 方法1：直接点击下拉菜单项
            await page.evaluate("""
                () => {
                    const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                    if (menuItem) {
                        menuItem.click();
                        return true;
                    }
                    return false;
                }
            """)
            logger.info("✅ 使用JavaScript成功点击訪問看護菜单")

        except Exception as e:
            logger.warning(f"⚠️ JavaScript点击失败，尝试传统方法: {e}")
            # 备用方法：传统hover+click
            await page.hover('.dropdown:nth-child(3) .dropdown-toggle')
            await page.wait_for_timeout(1000)
            await page.click('.dropdown:nth-child(3) li:nth-of-type(2) a')

        await page.wait_for_load_state("load")

        # 🆕 选择目标年月 - 使用8月
        target_reiwa_month = self._convert_to_reiwa_format("202508")
        logger.info(f"📅 选择月份: {target_reiwa_month}")

        # 使用智能选择器选择月份
        if not await self.selector_executor.smart_select_option(
            workflow="kaipoke_tennki",
            category="navigation",
            element="year_month_select",
            text=target_reiwa_month
        ):
            # 备用方法：直接使用选择器
            logger.warning("⚠️ 智能选择器失败，尝试直接选择")
            try:
                await page.select_option('#selectServiceOfferYm', label=target_reiwa_month)
                logger.info("✅ 备用方法成功选择月份")
            except Exception as e:
                logger.error(f"❌ 月份选择失败: {e}")
                raise Exception(f"无法选择月份: {target_reiwa_month}")

        await page.wait_for_load_state("load")
        logger.info("✅ 成功进入訪問看護页面")

    def _convert_to_reiwa_format(self, yyyymm: str) -> str:
        """
        将YYYYMM格式转换为令和年月格式
        例如: "202508" -> "令和7年8月"

        Args:
            yyyymm: YYYYMM格式的年月字符串

        Returns:
            str: 令和年月格式字符串
        """
        try:
            if len(yyyymm) != 6:
                raise ValueError(f"格式错误，期望YYYYMM格式，得到: {yyyymm}")

            year = int(yyyymm[:4])
            month = int(yyyymm[4:6])

            # 计算令和年 (2019年是令和元年，所以令和年 = 西历年 - 2018)
            reiwa_year = year - 2018

            # 格式化为令和年月（去掉月份前导零）
            reiwa_format = f"令和{reiwa_year}年{month}月"

            logger.debug(f"📅 年月转换: {yyyymm} -> {reiwa_format}")
            return reiwa_format

        except Exception as e:
            logger.error(f"❌ 令和年月转换失败: {e}")
            # 返回默认值
            return "令和7年8月"

    async def _handle_karte_component(self, page):
        """只处理Karte客服组件，完全保护数据登录表单"""
        try:
            # 只检查并隐藏Karte组件，不处理任何模态对话框
            karte_selectors = [
                '#karte-c',
                '.karte-widget__container',
                '.karte-c',
                '[id*="karte"]'
            ]

            for selector in karte_selectors:
                try:
                    if await page.locator(selector).count() > 0:
                        logger.info(f"🔍 发现Karte组件: {selector}")
                        # 使用JavaScript强制隐藏，但确保不影响数据登录表单
                        await page.evaluate(f"""
                            const elements = document.querySelectorAll('{selector}');
                            elements.forEach(element => {{
                                // 确保不是数据登录表单的一部分
                                if (!element.closest('#registModal') &&
                                    !element.querySelector('#inPopupInsuranceDivision01') &&
                                    !element.querySelector('#inPopupInsuranceDivision02')) {{
                                    element.style.display = 'none';
                                    element.style.visibility = 'hidden';
                                    element.style.zIndex = '-9999';
                                    element.style.opacity = '0';
                                    element.style.pointerEvents = 'none';
                                    element.remove();
                                }}
                            }});
                        """)
                        logger.info("✅ Karte组件已安全移除")
                        await page.wait_for_timeout(500)
                except Exception as e:
                    logger.debug(f"处理Karte组件失败 {selector}: {e}")
                    continue

            # 完全跳过任何模态对话框处理，保护数据登录表单
            logger.debug("🛡️ 跳过模态对话框处理，保护数据登录表单")
            return True

        except Exception as e:
            logger.debug(f"Karte组件处理过程中出错: {e}")
            return False

    # 🆕 移除弹窗处理装饰器和引擎，完全保护数据登录表单


class TennkiPerformanceMonitor:
    """Tennki性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.processed_records = 0
        self.failed_records = 0
        self.user_switches = 0
        
    def record_processed(self):
        """记录处理完成的记录"""
        self.processed_records += 1
        
    def record_failed(self):
        """记录失败的记录"""
        self.failed_records += 1
        
    def record_user_switch(self):
        """记录用户切换"""
        self.user_switches += 1
        
    def log_final_report(self, execution_time: float):
        """输出最终性能报告"""
        logger.info("📊 === 性能报告 ===")
        logger.info(f"⏱️ 总执行时间: {execution_time:.2f} 秒 ({execution_time/3600:.2f} 小时)")
        logger.info(f"✅ 处理成功: {self.processed_records} 条记录")
        logger.info(f"❌ 处理失败: {self.failed_records} 条记录")
        logger.info(f"👥 用户切换: {self.user_switches} 次")
        
        if self.processed_records > 0:
            avg_time = execution_time / self.processed_records
            logger.info(f"⚡ 平均处理时间: {avg_time:.2f} 秒/条")
            
        total_records = self.processed_records + self.failed_records
        if total_records > 0:
            success_rate = (self.processed_records / total_records) * 100
            logger.info(f"📈 成功率: {success_rate:.1f}%")
        else:
            logger.info("📈 成功率: 无数据处理")


def run(config: dict):
    """工作流入口函数（同步版本，供main.py调用）"""
    import asyncio

    async def async_run():
        """异步执行函数"""
        try:
            workflow_manager = TennkiWorkflowManager(config)
            await workflow_manager.execute_workflow()

        except Exception as e:
            logger.error(f"❌ Kaipoke Tennki工作流执行失败: {e}", exc_info=True)
            raise
        finally:
            # 清理资源
            try:
                await browser_manager.close()
            except:
                pass

    # 运行异步函数
    asyncio.run(async_run())


if __name__ == '__main__':
    # 测试配置
    test_config = {
        'login_url': 'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do',
        'spreadsheet_id': '1Qg1281kNwBV9tJIPaU7wL6A2AKrpj5bxdDRhT4N17fg',
        'sheet_name': '看護記録',
        'facilities': [
            {
                'name': '訪問看護/4660190861',
                'element_text': '訪問看護/4660190861'
            }
        ]
    }
    
    run(test_config)
