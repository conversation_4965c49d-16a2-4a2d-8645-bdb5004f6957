#!/usr/bin/env python3
"""
Kaipoke Tennki 数据登录表单弹窗保护测试脚本
测试修复后新規追加按钮点击后表单不会被误清除
"""

import asyncio
import logging
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_form_popup_protection():
    """测试数据登录表单弹窗保护"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            logger.info("🚀 开始测试数据登录表单弹窗保护")
            
            # 导航到测试页面
            logger.info("📍 导航到Kaipoke页面...")
            await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 测试1: 检查新規追加按钮是否存在
            logger.info("🔍 测试1: 检查新規追加按钮")
            
            add_button_selector = '#btn_area .cf:nth-child(1) :nth-child(1)'
            button_count = await page.locator(add_button_selector).count()
            
            if button_count > 0:
                logger.info(f"✅ 找到新規追加按钮: {button_count} 个")
            else:
                logger.warning("⚠️ 未找到新規追加按钮，可能需要登录或导航到正确页面")
                return False
            
            # 测试2: 点击新規追加按钮
            logger.info("🔍 测试2: 点击新規追加按钮")
            
            try:
                await page.click(add_button_selector, timeout=5000)
                logger.info("✅ 新規追加按钮点击成功")
                
                # 等待表单出现
                await page.wait_for_timeout(2000)
                
                # 测试3: 检查数据登录表单是否出现且保持可见
                logger.info("🔍 测试3: 检查数据登录表单状态")
                
                # 检查保险选择器是否可见
                kaigo_visible = await page.locator('#inPopupInsuranceDivision01').is_visible()
                iryou_visible = await page.locator('#inPopupInsuranceDivision02').is_visible()
                
                if kaigo_visible or iryou_visible:
                    logger.info("✅ 数据登录表单正常显示")
                    
                    # 等待一段时间，确保表单不会被误清除
                    logger.info("🔍 测试4: 等待5秒，确保表单不会被误清除")
                    await page.wait_for_timeout(5000)
                    
                    # 再次检查表单是否仍然可见
                    kaigo_still_visible = await page.locator('#inPopupInsuranceDivision01').is_visible()
                    iryou_still_visible = await page.locator('#inPopupInsuranceDivision02').is_visible()
                    
                    if kaigo_still_visible or iryou_still_visible:
                        logger.info("✅ 表单保护成功：5秒后表单仍然可见")
                        
                        # 测试5: 尝试选择保险类型
                        logger.info("🔍 测试5: 尝试选择医療保险")
                        
                        try:
                            if iryou_still_visible:
                                await page.click('#inPopupInsuranceDivision02')
                                logger.info("✅ 医療保险选择成功")
                                
                                # 等待表单字段激活
                                await page.wait_for_timeout(2000)
                                
                                # 检查表单字段是否激活
                                service_kind_visible = await page.locator('#inPopupServiceKindId').is_visible()
                                if service_kind_visible:
                                    logger.info("✅ 表单字段已激活")
                                else:
                                    logger.warning("⚠️ 表单字段未激活")
                                    
                            elif kaigo_still_visible:
                                await page.click('#inPopupInsuranceDivision01')
                                logger.info("✅ 介護保险选择成功")
                                
                        except Exception as e:
                            logger.warning(f"⚠️ 保险选择失败: {e}")
                            
                    else:
                        logger.error("❌ 表单保护失败：表单在5秒后消失了")
                        return False
                        
                else:
                    logger.error("❌ 数据登录表单未出现或立即消失")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ 新規追加按钮点击失败: {e}")
                return False
            
            logger.info("✅ 所有测试通过！表单弹窗保护机制正常工作")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
            
        finally:
            await browser.close()

async def main():
    """主函数"""
    logger.info("🧪 Kaipoke Tennki 数据登录表单弹窗保护测试")
    
    success = await test_form_popup_protection()
    
    if success:
        logger.info("🎉 测试成功！表单弹窗保护机制正常工作")
    else:
        logger.error("💥 测试失败，表单弹窗保护需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
