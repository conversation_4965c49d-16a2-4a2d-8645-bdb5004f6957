#!/usr/bin/env python3
"""
测试通知弹窗检测逻辑
用于验证修复后的通知弹窗检测和关闭功能
"""

import asyncio
import logging
from playwright.async_api import async_playwright

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_notification_detection():
    """测试通知弹窗检测逻辑"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            logger.info("🧪 开始测试通知弹窗检测逻辑...")
            
            # 模拟创建一个通知弹窗
            await page.set_content("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>通知弹窗测试</title>
                <style>
                    .modal {
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: white;
                        border: 2px solid #ccc;
                        padding: 20px;
                        z-index: 1000;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    }
                    .close {
                        position: absolute;
                        top: 10px;
                        right: 15px;
                        background: none;
                        border: none;
                        font-size: 20px;
                        cursor: pointer;
                    }
                    .backdrop {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        z-index: 999;
                    }
                </style>
            </head>
            <body>
                <h1>Kaipoke 月間スケジュール一覧页面测试</h1>
                
                <!-- 模拟通知弹窗 -->
                <div class="backdrop"></div>
                <div class="modal" id="notificationModal">
                    <button class="close" onclick="closeModal()">×</button>
                    <h3 style="color: red;">【重要】</h3>
                    <p>訪問看護出張所（サテライト）<br>の<br>料金体系についてのお知らせ</p>
                    <p>2025年7月ご利用分（8月請求分）<br>より、料金体系が変更となりました。</p>
                    <p>カイポケの利用料金についての重要<br>なご案内がございます。詳細は以下<br>のお知らせをご一読いただけます<br>ようお願いします。</p>
                    <button onclick="closeModal()" style="background: #333; color: white; padding: 10px 20px; border: none; margin-top: 10px;">
                        お知らせはこちら
                    </button>
                </div>
                
                <!-- 模拟新规追加按钮 -->
                <div id="btn_area">
                    <div class="cf">
                        <button onclick="alert('新规追加点击成功!')">新規追加</button>
                    </div>
                </div>
                
                <script>
                    function closeModal() {
                        document.getElementById('notificationModal').style.display = 'none';
                        document.querySelector('.backdrop').style.display = 'none';
                        console.log('通知弹窗已关闭');
                    }
                </script>
            </body>
            </html>
            """)
            
            logger.info("✅ 测试页面已加载")
            
            # 等待页面加载完成
            await page.wait_for_timeout(2000)
            
            # 测试智能通知弹窗检测
            logger.info("🔍 测试智能通知弹窗检测...")
            
            notification_modal_detected = await page.evaluate("""
                () => {
                    // 查找包含通知关键词的模态框
                    const modals = document.querySelectorAll('.modal, [class*="modal"], .popup, [class*="popup"]');
                    
                    for (let modal of modals) {
                        const text = modal.textContent || '';
                        const isNotificationModal = text.includes('重要') || 
                                                  text.includes('お知らせ') || 
                                                  text.includes('訪問看護出張所') ||
                                                  text.includes('サテライト') ||
                                                  text.includes('料金体系');
                        
                        if (isNotificationModal) {
                            // 查找这个模态框内的关闭按钮
                            const closeButtons = modal.querySelectorAll('button, [class*="close"], [onclick*="close"]');
                            for (let btn of closeButtons) {
                                if (btn.textContent.includes('×') || 
                                    btn.textContent.includes('閉じる') ||
                                    btn.className.includes('close') ||
                                    btn.getAttribute('aria-label') === 'Close') {
                                    return {
                                        found: true,
                                        modalText: text.substring(0, 100),
                                        buttonText: btn.textContent,
                                        buttonClass: btn.className
                                    };
                                }
                            }
                        }
                    }
                    return { found: false };
                }
            """)
            
            if notification_modal_detected.get('found'):
                logger.info(f"✅ 智能检测到通知弹窗: {notification_modal_detected.get('modalText', '未知内容')[:50]}...")
                logger.info(f"🔘 关闭按钮: {notification_modal_detected.get('buttonText')} (class: {notification_modal_detected.get('buttonClass')})")
                
                # 测试关闭弹窗
                close_button_clicked = await page.evaluate("""
                    () => {
                        const modals = document.querySelectorAll('.modal, [class*="modal"], .popup, [class*="popup"]');
                        
                        for (let modal of modals) {
                            const text = modal.textContent || '';
                            const isNotificationModal = text.includes('重要') || 
                                                      text.includes('お知らせ') || 
                                                      text.includes('訪問看護出張所') ||
                                                      text.includes('サテライト') ||
                                                      text.includes('料金体系');
                            
                            if (isNotificationModal) {
                                const closeButtons = modal.querySelectorAll('button, [class*="close"], [onclick*="close"]');
                                for (let btn of closeButtons) {
                                    if (btn.textContent.includes('×') || 
                                        btn.textContent.includes('閉じる') ||
                                        btn.className.includes('close') ||
                                        btn.getAttribute('aria-label') === 'Close') {
                                        btn.click();
                                        return true;
                                    }
                                }
                            }
                        }
                        return false;
                    }
                """)
                
                if close_button_clicked:
                    logger.info("✅ 智能检测通知弹窗已关闭")
                    await page.wait_for_timeout(1500)
                    
                    # 验证弹窗是否真的关闭了
                    modal_still_visible = await page.locator('#notificationModal').is_visible()
                    if not modal_still_visible:
                        logger.info("🎉 测试成功：通知弹窗已完全关闭")
                    else:
                        logger.error("❌ 测试失败：通知弹窗仍然可见")
                else:
                    logger.error("❌ 测试失败：无法关闭通知弹窗")
            else:
                logger.error("❌ 测试失败：未检测到通知弹窗")
            
            # 等待用户观察结果
            logger.info("⏳ 等待10秒供观察结果...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_notification_detection())
