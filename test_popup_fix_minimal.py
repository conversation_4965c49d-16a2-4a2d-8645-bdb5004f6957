#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kaipoke Tennki 新规追加窗口保护测试脚本
测试最小化弹窗处理策略的效果

时间戳: 2025-07-24 12:00:00 JST
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_minimal_popup_handling():
    """测试最小化弹窗处理策略"""
    playwright = None
    browser = None

    try:
        logger.info("🚀 开始测试最小化弹窗处理策略...")

        # 1. 初始化浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 2. 导航到kaipoke登录页面
        logger.info("🌐 导航到kaipoke登录页面...")
        await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
        
        # 3. 等待页面加载
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        
        # 4. 测试お知らせ通知处理
        logger.info("🔍 测试お知らせ通知处理...")

        # 简单的お知らせ通知检测和关闭
        try:
            oshirase_count = await page.locator('._icon-close__bF1y_').count()
            if oshirase_count > 0:
                await page.click('._icon-close__bF1y_', timeout=3000)
                logger.info("✅ お知らせ通知已关闭")
        except Exception as e:
            logger.debug(f"お知らせ通知处理: {e}")
        
        # 6. 检查页面状态
        logger.info("🔍 检查页面状态...")
        
        # 检查是否有お知らせ通知
        oshirase_count = await page.locator('._icon-close__bF1y_').count()
        logger.info(f"📊 お知らせ通知数量: {oshirase_count}")
        
        # 检查是否有数据登录窗口
        modal_count = await page.locator('#registModal').count()
        modal_visible = await page.locator('#registModal').is_visible() if modal_count > 0 else False
        logger.info(f"📊 数据登录窗口: 存在={modal_count > 0}, 可见={modal_visible}")
        
        # 检查保险选择器
        insurance_count = await page.locator('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').count()
        logger.info(f"📊 保险选择器数量: {insurance_count}")
        
        # 7. 测试新规追加按钮点击（如果可能）
        add_button_count = await page.locator('#btn_area .cf:nth-child(1) :nth-child(1)').count()
        if add_button_count > 0:
            logger.info("🔘 发现新规追加按钮，测试点击...")
            try:
                # 使用简化的点击逻辑
                await page.click('#btn_area .cf:nth-child(1) :nth-child(1)', timeout=5000)
                logger.info("✅ 新规追加按钮点击成功")

                # 等待表单出现
                await page.wait_for_selector('#registModal', timeout=10000, state='visible')
                logger.info("✅ 数据登录表单已出现")

                # 检查表单是否保持可见（多次检查）
                modal_still_visible = True
                for i in range(5):  # 检查5次，每次间隔1秒
                    await page.wait_for_timeout(1000)
                    current_visible = await page.locator('#registModal').is_visible()
                    logger.info(f"📊 第{i+1}次检查 - 表单可见: {current_visible}")
                    if not current_visible:
                        modal_still_visible = False
                        break

                if modal_still_visible:
                    logger.info("🎉 测试成功：数据登录表单保持可见，未被误清除！")

                    # 额外检查保险选择器
                    insurance_visible = await page.locator('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').is_visible()
                    logger.info(f"📊 保险选择器可见: {insurance_visible}")

                else:
                    logger.error("❌ 测试失败：数据登录表单被误清除")

            except Exception as e:
                logger.error(f"❌ 新规追加按钮点击失败: {e}")
                modal_still_visible = False
        else:
            logger.warning("⚠️ 未找到新规追加按钮")
            modal_still_visible = None
        
        # 8. 生成测试报告
        logger.info("📋 生成测试报告...")
        
        test_results = {
            "oshirase_notifications": oshirase_count,
            "modal_exists": modal_count > 0,
            "modal_visible": modal_visible,
            "insurance_selectors": insurance_count,
            "add_button_exists": add_button_count > 0,
            "test_passed": modal_still_visible if 'modal_still_visible' in locals() else None
        }
        
        logger.info("📊 测试结果:")
        for key, value in test_results.items():
            logger.info(f"   {key}: {value}")
            
        return test_results
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return None

    finally:
        if browser:
            try:
                await browser.close()
                logger.info("🔒 浏览器已关闭")
            except:
                pass
        if playwright:
            try:
                await playwright.stop()
            except:
                pass

async def main():
    """主函数"""
    logger.info("🔧 Kaipoke Tennki 最小化弹窗处理测试")
    logger.info("=" * 50)
    
    results = await test_minimal_popup_handling()
    
    if results:
        logger.info("✅ 测试完成")
        if results.get('test_passed'):
            logger.info("🎉 修复成功：数据登录表单保护机制工作正常！")
        else:
            logger.warning("⚠️ 需要进一步调试")
    else:
        logger.error("❌ 测试失败")

if __name__ == "__main__":
    asyncio.run(main())
