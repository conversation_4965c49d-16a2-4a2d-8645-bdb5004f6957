#!/usr/bin/env python3
"""
Kaipoke Tennki表单状态检测修复验证脚本
测试新的状态检测逻辑是否能正确识别数据登录窗口状态
"""

import asyncio
import logging
from core.browser.browser_manager import browser_manager
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
from core.selector_executor import SelectorExecutor

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_form_state_detection():
    """测试表单状态检测功能"""
    logger.info("🧪 开始测试Kaipoke Tennki表单状态检测修复")
    
    try:
        # 1. 初始化浏览器和组件
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        # 创建一个简单的性能监控对象
        class SimplePerformanceMonitor:
            def log_performance(self, *args, **kwargs):
                pass
        performance_monitor = SimplePerformanceMonitor()
        form_engine = TennkiFormEngine(selector_executor, performance_monitor)
        
        # 2. 登录到Kaipoke系统
        logger.info("🔐 登录到Kaipoke系统...")
        login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
        await page.goto(login_url)
        
        # 这里需要手动登录或使用环境变量
        logger.info("⚠️ 请手动完成登录，然后按Enter继续...")
        input("按Enter继续...")
        
        # 3. 导航到测试页面
        logger.info("🧭 导航到訪問看護页面...")
        # 这里可以添加导航逻辑
        
        # 4. 测试状态检测功能
        logger.info("🔍 测试1: 检测初始状态（应该没有数据登录窗口）")
        is_window_open_1 = await form_engine._is_data_entry_form_window_open(page)
        is_form_active_1 = await form_engine._is_data_entry_form_active(page)
        
        logger.info(f"📊 初始状态检测结果:")
        logger.info(f"   - 数据登录窗口打开: {is_window_open_1}")
        logger.info(f"   - 表单元素活跃: {is_form_active_1}")
        
        # 5. 点击新規追加按钮
        logger.info("🔍 测试2: 点击新規追加按钮后的状态检测")
        try:
            await form_engine._click_add_button()
            await page.wait_for_timeout(2000)  # 等待表单加载
            
            is_window_open_2 = await form_engine._is_data_entry_form_window_open(page)
            is_form_active_2 = await form_engine._is_data_entry_form_active(page)
            
            logger.info(f"📊 点击新規追加后状态检测结果:")
            logger.info(f"   - 数据登录窗口打开: {is_window_open_2}")
            logger.info(f"   - 表单元素活跃: {is_form_active_2}")
            
        except Exception as e:
            logger.error(f"❌ 点击新規追加按钮失败: {e}")
        
        # 6. 测试重复点击保护
        logger.info("🔍 测试3: 重复点击保护机制")
        try:
            # 模拟处理记录的调用
            test_record = {
                'raw_data': {'保険種別': '医療', 'test': 'data'},
                'row_index': 999
            }
            
            logger.info("🔄 第一次调用 _process_single_record...")
            await form_engine._process_single_record(test_record, '医療')
            
        except Exception as e:
            logger.info(f"📝 第一次调用结果: {e}")
        
        # 7. 检查最终状态
        logger.info("🔍 测试4: 最终状态检测")
        is_window_open_final = await form_engine._is_data_entry_form_window_open(page)
        is_form_active_final = await form_engine._is_data_entry_form_active(page)
        
        logger.info(f"📊 最终状态检测结果:")
        logger.info(f"   - 数据登录窗口打开: {is_window_open_final}")
        logger.info(f"   - 表单元素活跃: {is_form_active_final}")
        
        # 8. 总结测试结果
        logger.info("📋 测试总结:")
        logger.info("✅ 状态检测功能测试完成")
        logger.info("✅ 重复点击保护机制测试完成")
        
        if is_window_open_2 and not is_window_open_1:
            logger.info("✅ 状态检测逻辑工作正常：能正确识别窗口打开状态")
        else:
            logger.warning("⚠️ 状态检测可能需要进一步调整")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        raise
    
    finally:
        # 清理资源
        try:
            await browser_manager.close()
        except:
            pass

async def test_form_protection_logic():
    """测试表单保护逻辑"""
    logger.info("🛡️ 测试表单保护逻辑")
    
    try:
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        # 创建一个简单的性能监控对象
        class SimplePerformanceMonitor:
            def log_performance(self, *args, **kwargs):
                pass
        performance_monitor = SimplePerformanceMonitor()
        form_engine = TennkiFormEngine(selector_executor, performance_monitor)
        
        # 测试保护模式清理
        logger.info("🧹 测试保护模式清理功能")
        await form_engine._protected_cleanup(page)
        logger.info("✅ 保护模式清理测试完成")
        
        # 测试标准清理
        logger.info("🧹 测试标准清理功能")
        await form_engine._standard_cleanup(page)
        logger.info("✅ 标准清理测试完成")
        
    except Exception as e:
        logger.error(f"❌ 表单保护逻辑测试失败: {e}")
        raise

if __name__ == "__main__":
    print("🧪 Kaipoke Tennki表单状态检测修复验证")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_form_state_detection())
    
    print("\n🛡️ 表单保护逻辑测试")
    print("=" * 50)
    asyncio.run(test_form_protection_logic())
    
    print("\n✅ 所有测试完成")
