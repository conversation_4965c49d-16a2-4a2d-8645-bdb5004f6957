#!/usr/bin/env python3
"""
Kaipoke Tennki 新規追加按钮选择器修复测试脚本
测试修复后的选择器和弹窗处理逻辑
"""

import asyncio
import logging
from playwright.async_api import async_playwright
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
from core.selector_executor import SelectorExecutor

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_selector_fix():
    """测试新規追加按钮选择器修复"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 初始化选择器执行器和表单引擎
            selector_executor = SelectorExecutor(page)
            form_engine = TennkiFormEngine(selector_executor)
            
            logger.info("🚀 开始测试新規追加按钮选择器修复")
            
            # 导航到测试页面（这里需要实际的kaipoke页面）
            logger.info("📍 导航到Kaipoke页面...")
            await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 测试1: 检查页面上是否存在新規追加按钮
            logger.info("🔍 测试1: 检查新規追加按钮是否存在")
            
            button_selectors = [
                '#btn_area .cf:nth-child(1) :nth-child(1)',  # 用户推荐的选择器
                'button:has-text("新規追加")',
                'input[value="新規追加"]',
                '.btn:has-text("新規追加")',
                '[onclick*="新規追加"]'
            ]
            
            found_selectors = []
            for selector in button_selectors:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        found_selectors.append(selector)
                        logger.info(f"✅ 找到按钮: {selector} (数量: {count})")
                    else:
                        logger.debug(f"❌ 未找到: {selector}")
                except Exception as e:
                    logger.debug(f"⚠️ 选择器错误: {selector}, {e}")
            
            if found_selectors:
                logger.info(f"✅ 测试1通过: 找到 {len(found_selectors)} 个有效选择器")
            else:
                logger.error("❌ 测试1失败: 未找到任何新規追加按钮")
                return False
            
            # 测试2: 检查弹窗检测逻辑
            logger.info("🔍 测试2: 检查弹窗检测逻辑")
            
            popup_detected = await form_engine._check_for_popups(page)
            if popup_detected:
                logger.info("✅ 检测到弹窗，将进行清理")
            else:
                logger.info("✅ 未检测到干扰弹窗，可以直接操作")
            
            # 测试3: 测试新規追加按钮点击（如果在正确页面）
            logger.info("🔍 测试3: 测试新規追加按钮点击逻辑")
            
            try:
                # 检查是否在正确的页面（有新規追加按钮）
                if found_selectors:
                    logger.info("🔘 尝试点击新規追加按钮...")
                    
                    # 使用第一个找到的选择器进行测试
                    test_selector = found_selectors[0]
                    await page.click(test_selector, timeout=3000)
                    logger.info(f"✅ 成功点击新規追加按钮: {test_selector}")
                    
                    # 等待表单加载
                    await page.wait_for_timeout(2000)
                    
                    # 检查表单是否加载
                    form_loaded = await page.locator('#inPopupInsuranceDivision01').is_visible() or \
                                 await page.locator('#inPopupInsuranceDivision02').is_visible()
                    
                    if form_loaded:
                        logger.info("✅ 表单成功加载")
                    else:
                        logger.warning("⚠️ 表单未加载，可能需要登录或在错误页面")
                        
                else:
                    logger.warning("⚠️ 跳过点击测试：未找到新規追加按钮")
                    
            except Exception as e:
                logger.warning(f"⚠️ 点击测试失败: {e}")
            
            logger.info("✅ 测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
            
        finally:
            await browser.close()

async def main():
    """主函数"""
    logger.info("🧪 Kaipoke Tennki 新規追加按钮选择器修复测试")
    
    success = await test_selector_fix()
    
    if success:
        logger.info("🎉 所有测试通过！修复成功")
    else:
        logger.error("💥 测试失败，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
