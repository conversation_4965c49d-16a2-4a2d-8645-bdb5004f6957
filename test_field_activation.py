#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段激活功能的脚本
验证保险选择器是否能正确激活

时间戳: 2025-07-24 16:30:00 JST
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_field_activation():
    """测试字段激活功能"""
    playwright = None
    browser = None
    
    try:
        logger.info("🔍 开始测试字段激活功能...")
        
        # 1. 初始化浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 2. 导航到kaipoke登录页面
        logger.info("🌐 导航到kaipoke登录页面...")
        await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
        
        # 3. 等待页面加载
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        
        # 4. 创建模拟的数据登录表单（包含disabled字段）
        logger.info("🔧 创建模拟的数据登录表单...")
        await page.evaluate("""
            () => {
                // 创建模拟的registModal
                const modal = document.createElement('div');
                modal.id = 'registModal';
                modal.className = 'modal fade in';
                modal.style.cssText = `
                    display: block !important;
                    position: fixed;
                    top: 50px;
                    left: 50px;
                    width: 600px;
                    height: 500px;
                    background: white;
                    border: 2px solid blue;
                    z-index: 9999;
                    padding: 20px;
                    overflow-y: auto;
                `;
                modal.innerHTML = `
                    <h3>模拟数据登录表单</h3>
                    <div style="margin: 10px 0;">
                        <h4>保险区分 (初始状态: disabled)</h4>
                        <input id="inPopupInsuranceDivision01" type="radio" name="insurance" value="1" disabled>
                        <label for="inPopupInsuranceDivision01">介護保险</label><br>
                        <input id="inPopupInsuranceDivision02" type="radio" name="insurance" value="2" disabled>
                        <label for="inPopupInsuranceDivision02">医疗保险</label><br>
                        <input id="inPopupInsuranceDivision03" type="radio" name="insurance" value="3" disabled>
                        <label for="inPopupInsuranceDivision03">自费</label><br>
                    </div>
                    <div style="margin: 10px 0;">
                        <h4>下拉选择器 (初始状态: disabled)</h4>
                        <select id="testSelect1" disabled>
                            <option value="">请选择</option>
                            <option value="1">选项1</option>
                            <option value="2">选项2</option>
                        </select><br><br>
                        <select id="testSelect2" disabled>
                            <option value="">请选择服务</option>
                            <option value="nursing">訪問看護</option>
                            <option value="care">介護</option>
                        </select>
                    </div>
                    <div style="margin: 10px 0;">
                        <h4>输入框 (初始状态: disabled)</h4>
                        <input type="text" id="testInput1" placeholder="测试输入1" disabled><br><br>
                        <input type="number" id="testInput2" placeholder="测试输入2" disabled>
                    </div>
                    <div style="margin: 10px 0;">
                        <button id="btnRegisPop" disabled>登录</button>
                        <button id="testActivate" onclick="testActivation()">测试激活</button>
                    </div>
                `;
                document.body.appendChild(modal);
                
                // 添加测试激活函数
                window.testActivation = function() {
                    console.log('开始测试激活...');
                    
                    // 激活保险选择器
                    const insuranceRadios = document.querySelectorAll('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02, #inPopupInsuranceDivision03');
                    insuranceRadios.forEach(radio => {
                        radio.removeAttribute('disabled');
                        radio.disabled = false;
                        radio.style.pointerEvents = 'auto';
                        radio.style.opacity = '1';
                        radio.style.cursor = 'pointer';
                    });

                    // 激活下拉选择器
                    const selects = document.querySelectorAll('#registModal select');
                    selects.forEach(select => {
                        select.removeAttribute('disabled');
                        select.disabled = false;
                        select.style.pointerEvents = 'auto';
                        select.style.opacity = '1';
                        select.style.backgroundColor = 'white';
                    });

                    // 激活输入框
                    const inputs = document.querySelectorAll('#registModal input');
                    inputs.forEach(input => {
                        input.removeAttribute('disabled');
                        input.disabled = false;
                        input.style.pointerEvents = 'auto';
                        input.style.opacity = '1';
                        input.style.backgroundColor = 'white';
                    });
                    
                    console.log('激活完成');
                };
                
                console.log('✅ 模拟数据登录表单已创建（字段初始为disabled状态）');
                return true;
            }
        """)
        
        # 5. 检查初始状态
        logger.info("🔍 检查字段初始状态...")
        initial_state = await page.evaluate("""
            () => {
                const results = {};
                
                // 检查保险选择器
                const insurance1 = document.querySelector('#inPopupInsuranceDivision01');
                const insurance2 = document.querySelector('#inPopupInsuranceDivision02');
                const insurance3 = document.querySelector('#inPopupInsuranceDivision03');
                
                results.insurance_disabled = [
                    insurance1 ? insurance1.disabled : null,
                    insurance2 ? insurance2.disabled : null,
                    insurance3 ? insurance3.disabled : null
                ];
                
                // 检查下拉选择器
                const select1 = document.querySelector('#testSelect1');
                const select2 = document.querySelector('#testSelect2');
                
                results.select_disabled = [
                    select1 ? select1.disabled : null,
                    select2 ? select2.disabled : null
                ];
                
                // 检查输入框
                const input1 = document.querySelector('#testInput1');
                const input2 = document.querySelector('#testInput2');
                
                results.input_disabled = [
                    input1 ? input1.disabled : null,
                    input2 ? input2.disabled : null
                ];
                
                return results;
            }
        """)
        
        logger.info(f"📊 初始状态 - 保险选择器disabled: {initial_state['insurance_disabled']}")
        logger.info(f"📊 初始状态 - 下拉选择器disabled: {initial_state['select_disabled']}")
        logger.info(f"📊 初始状态 - 输入框disabled: {initial_state['input_disabled']}")
        
        # 6. 测试我们的安全激活函数
        logger.info("🔧 测试安全激活函数...")
        
        # 模拟我们的激活逻辑
        activation_result = await page.evaluate("""
            () => {
                let activatedCount = 0;
                let results = [];

                // 1. 激活保险选择器
                const insuranceRadios = document.querySelectorAll('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02, #inPopupInsuranceDivision03');
                insuranceRadios.forEach((radio, index) => {
                    const before = radio.disabled;
                    radio.removeAttribute('disabled');
                    radio.disabled = false;
                    radio.style.pointerEvents = 'auto';
                    radio.style.opacity = '1';
                    radio.style.cursor = 'pointer';
                    
                    results.push({
                        type: 'insurance_radio',
                        index: index,
                        before_disabled: before,
                        after_disabled: radio.disabled
                    });
                    activatedCount++;
                });

                // 2. 激活所有下拉选择器
                const selects = document.querySelectorAll('#registModal select');
                selects.forEach((select, index) => {
                    const before = select.disabled;
                    select.removeAttribute('disabled');
                    select.disabled = false;
                    select.style.pointerEvents = 'auto';
                    select.style.opacity = '1';
                    select.style.backgroundColor = 'white';
                    
                    results.push({
                        type: 'select',
                        index: index,
                        before_disabled: before,
                        after_disabled: select.disabled
                    });
                    activatedCount++;
                });

                // 3. 激活所有输入框
                const inputs = document.querySelectorAll('#registModal input');
                inputs.forEach((input, index) => {
                    const before = input.disabled;
                    input.removeAttribute('disabled');
                    input.disabled = false;
                    input.style.pointerEvents = 'auto';
                    input.style.opacity = '1';
                    input.style.backgroundColor = 'white';
                    
                    results.push({
                        type: 'input',
                        index: index,
                        before_disabled: before,
                        after_disabled: input.disabled
                    });
                    activatedCount++;
                });

                return {
                    activatedCount: activatedCount,
                    results: results
                };
            }
        """)
        
        logger.info(f"✅ 激活完成: {activation_result['activatedCount']} 个字段已激活")
        
        # 记录激活详情
        for result in activation_result['results']:
            if result['before_disabled'] and not result['after_disabled']:
                logger.info(f"✅ {result['type']}[{result['index']}] 从disabled变为enabled")
        
        # 7. 检查激活后状态
        logger.info("🔍 检查字段激活后状态...")
        final_state = await page.evaluate("""
            () => {
                const results = {};
                
                // 检查保险选择器
                const insurance1 = document.querySelector('#inPopupInsuranceDivision01');
                const insurance2 = document.querySelector('#inPopupInsuranceDivision02');
                const insurance3 = document.querySelector('#inPopupInsuranceDivision03');
                
                results.insurance_disabled = [
                    insurance1 ? insurance1.disabled : null,
                    insurance2 ? insurance2.disabled : null,
                    insurance3 ? insurance3.disabled : null
                ];
                
                results.insurance_clickable = [
                    insurance1 ? insurance1.style.pointerEvents !== 'none' : null,
                    insurance2 ? insurance2.style.pointerEvents !== 'none' : null,
                    insurance3 ? insurance3.style.pointerEvents !== 'none' : null
                ];
                
                return results;
            }
        """)
        
        logger.info(f"📊 激活后状态 - 保险选择器disabled: {final_state['insurance_disabled']}")
        logger.info(f"📊 激活后状态 - 保险选择器clickable: {final_state['insurance_clickable']}")
        
        # 8. 测试实际点击
        logger.info("🔘 测试保险选择器点击...")
        try:
            await page.click('#inPopupInsuranceDivision01', timeout=5000)
            logger.info("✅ 介護保险选择器点击成功")
            
            # 检查是否被选中
            is_checked = await page.locator('#inPopupInsuranceDivision01').is_checked()
            logger.info(f"📊 介護保险选择器选中状态: {is_checked}")
            
        except Exception as e:
            logger.error(f"❌ 介護保险选择器点击失败: {e}")
        
        # 9. 等待用户观察
        logger.info("⏳ 等待30秒供用户观察和测试...")
        await page.wait_for_timeout(30000)
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        if browser:
            try:
                await browser.close()
                logger.info("🔒 浏览器已关闭")
            except:
                pass
        if playwright:
            try:
                await playwright.stop()
            except:
                pass

async def main():
    """主函数"""
    logger.info("🔧 Kaipoke Tennki 字段激活功能测试")
    logger.info("=" * 50)
    
    await test_field_activation()

if __name__ == "__main__":
    asyncio.run(main())
