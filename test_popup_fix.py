#!/usr/bin/env python3
"""
测试kaipoke_tennki_refactored工作流的弹窗处理修复
验证"新規追加"按钮点击后数据登录表单不会被误清除
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
# from core.rpa_tools.performance_monitor import TennkiPerformanceMonitor


async def test_popup_handling():
    """测试弹窗处理修复"""
    logger.info("🧪 开始测试弹窗处理修复...")
    
    try:
        # 1. 启动浏览器
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        
        # 初始化MCP备份工具
        await selector_executor.initialize_mcp_fallback()
        
        # 2. 登录Kaipoke
        login_success = await kaipoke_login_with_env(
            page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
        
        logger.info("✅ 登录成功")
        
        # 3. 导航到测试页面
        await page.goto('https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true')
        await page.wait_for_load_state("load")
        
        # 4. 选择据点
        await page.click('text="訪問看護/4660190861"')
        await page.wait_for_load_state("load")
        
        # 5. 导航到訪問看護页面
        await page.evaluate("""
            () => {
                const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                if (menuItem) {
                    menuItem.click();
                    return true;
                }
                return false;
            }
        """)
        await page.wait_for_load_state("load")
        
        # 6. 选择月份
        await page.select_option('#selectServiceOfferYm', label='令和7年8月')
        await page.wait_for_load_state("load")
        
        # 7. 初始化表单引擎
        # performance_monitor = TennkiPerformanceMonitor()
        form_engine = TennkiFormEngine(selector_executor, None)
        
        # 8. 测试新規追加按钮点击
        logger.info("🧪 测试新規追加按钮点击...")
        
        # 记录点击前的页面状态
        before_click = await page.evaluate("""
            () => {
                return {
                    modalCount: document.querySelectorAll('.modal').length,
                    registModalVisible: document.querySelector('#registModal') ? 
                        window.getComputedStyle(document.querySelector('#registModal')).display : 'none'
                };
            }
        """)
        
        logger.info(f"点击前状态: {before_click}")
        
        # 执行点击操作
        await form_engine._click_add_button()
        
        # 等待一段时间观察表单状态
        await page.wait_for_timeout(3000)
        
        # 记录点击后的页面状态
        after_click = await page.evaluate("""
            () => {
                const modal = document.querySelector('#registModal');
                return {
                    modalCount: document.querySelectorAll('.modal').length,
                    registModalVisible: modal ? window.getComputedStyle(modal).display : 'none',
                    registModalExists: !!modal,
                    insuranceFieldsCount: document.querySelectorAll('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').length,
                    formProtected: modal ? modal.getAttribute('data-form-protected') : null
                };
            }
        """)
        
        logger.info(f"点击后状态: {after_click}")
        
        # 9. 验证结果
        if after_click['registModalExists'] and after_click['registModalVisible'] != 'none':
            logger.info("✅ 测试成功: 数据登录表单保持打开状态")
            
            if after_click['insuranceFieldsCount'] >= 2:
                logger.info("✅ 测试成功: 保险选择字段正常存在")
            else:
                logger.warning("⚠️ 警告: 保险选择字段可能缺失")
                
            if after_click['formProtected']:
                logger.info("✅ 测试成功: 表单保护机制已激活")
            else:
                logger.warning("⚠️ 警告: 表单保护机制未激活")
                
        else:
            logger.error("❌ 测试失败: 数据登录表单被误清除")
            return False
        
        # 10. 测试保险类型选择
        logger.info("🧪 测试保险类型选择...")

        try:
            # 尝试选择医療保险
            await page.click('#inPopupInsuranceDivision02', timeout=5000)
            logger.info("✅ 医療保险选择成功")

            # 检查表单是否仍然存在
            form_still_exists = await page.locator('#registModal').is_visible()
            if form_still_exists:
                logger.info("✅ 保险选择后表单仍然存在")
            else:
                logger.error("❌ 保险选择后表单被清除")
                return False

        except Exception as e:
            logger.error(f"❌ 保险类型选择测试失败: {e}")
            return False

        # 11. 测试实施日和职员情报流程
        logger.info("🧪 测试实施日和职员情报流程...")

        try:
            # 模拟一个简单的日期选择器
            date_selector = ".ui-state-default:first-child"

            # 检查实施日选择前职员情报字段状态
            staff_before = await page.evaluate("""
                () => {
                    const staffButton = document.querySelector('#input_staff_on > input');
                    return {
                        exists: !!staffButton,
                        enabled: staffButton ? !staffButton.disabled : false
                    };
                }
            """)
            logger.info(f"实施日选择前职员情报状态: {staff_before}")

            # 尝试选择实施日（如果日期选择器存在）
            try:
                await page.click(date_selector, timeout=3000)
                logger.info("✅ 实施日选择成功")

                # 等待switchPlanAct函数执行
                await page.wait_for_timeout(1000)

                # 检查职员情报字段是否被激活
                staff_after = await page.evaluate("""
                    () => {
                        const staffButton = document.querySelector('#input_staff_on > input');
                        return {
                            exists: !!staffButton,
                            enabled: staffButton ? !staffButton.disabled : false
                        };
                    }
                """)
                logger.info(f"实施日选择后职员情报状态: {staff_after}")

                if staff_after['enabled']:
                    logger.info("✅ 职员情报字段已正确激活")
                else:
                    logger.warning("⚠️ 职员情报字段未激活，可能需要手动触发")

            except Exception as date_e:
                logger.warning(f"⚠️ 实施日选择失败（可能日期选择器不存在）: {date_e}")

        except Exception as e:
            logger.error(f"❌ 实施日和职员情报流程测试失败: {e}")
            return False
        
        logger.info("✅ 所有测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
        
    finally:
        # 清理资源
        try:
            await browser_manager.close()
        except:
            pass


async def main():
    """主函数"""
    logger.info("🚀 开始弹窗处理修复测试")
    
    success = await test_popup_handling()
    
    if success:
        logger.info("🎉 弹窗处理修复测试完成 - 所有测试通过")
        sys.exit(0)
    else:
        logger.error("💥 弹窗处理修复测试失败")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
