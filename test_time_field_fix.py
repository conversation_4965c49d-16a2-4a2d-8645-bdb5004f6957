#!/usr/bin/env python3
"""
Kaipoke Tennki时间字段修复测试脚本
测试智能事件拦截机制是否能解决时间选择导致表单disabled的问题

测试场景：
1. 时间字段能够完整填写
2. 填写后其他表单字段保持enabled状态  
3. 保险区分选择器正常工作
4. 事件拦截器正确恢复
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env


class TimeFieldFixTester:
    """时间字段修复测试器"""
    
    def __init__(self):
        self.form_engine = None
        self.page = None
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🔧 初始化测试环境...")

        # 启动浏览器
        await browser_manager.launch(headless=False)
        self.page = await browser_manager.get_page()

        # 登录Kaipoke
        await kaipoke_login_with_env(self.page)

        # 初始化表单引擎
        selector_executor = SelectorExecutor(self.page)
        self.form_engine = TennkiFormEngine(selector_executor)

        logger.info("✅ 测试环境初始化完成")
        
    async def navigate_to_form(self):
        """导航到数据登录表单"""
        logger.info("🧭 导航到数据登录表单...")
        
        try:
            # 导航到据点选择
            await self.page.click('.mainCtg li:nth-of-type(1) a')
            await self.page.wait_for_timeout(1000)
            
            # 选择测试据点（訪問看護）
            await self.page.click('xpath=//a[contains(text(), "訪問看護/4660190861")]')
            await self.page.wait_for_timeout(2000)
            
            # 选择月份
            await self.page.select_option('#selectServiceOfferYm', label='令和7年8月')
            await self.page.wait_for_timeout(1000)
            
            # 选择用户
            await self.page.select_option('.pulldownUser .form-control', index=1)
            await self.page.wait_for_timeout(1000)
            
            # 点击新規追加按钮
            await self.page.click('#btn_area .cf:nth-child(1) :nth-child(1)')
            await self.page.wait_for_selector('#registModal', timeout=10000, state='visible')
            
            logger.info("✅ 成功导航到数据登录表单")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导航到表单失败: {e}")
            return False
    
    async def test_time_field_interceptor(self):
        """测试时间字段事件拦截器"""
        logger.info("🧪 测试时间字段事件拦截器...")
        
        try:
            # 测试拦截器创建
            interceptor_ready = await self.form_engine._create_time_field_interceptor(self.page)
            assert interceptor_ready, "事件拦截器创建失败"
            
            # 测试状态保护
            protection_active = await self.form_engine._protect_form_state_during_time_fill(self.page)
            assert protection_active, "表单状态保护失败"
            
            # 验证拦截器状态
            interceptor_status = await self.page.evaluate("""
                () => {
                    return window.timeFieldInterceptor ? {
                        exists: true,
                        isActive: window.timeFieldInterceptor.isActive,
                        hasFunctions: !!window.timeFieldInterceptor.originalFunctions
                    } : { exists: false };
                }
            """)
            
            assert interceptor_status['exists'], "拦截器对象不存在"
            assert interceptor_status['isActive'], "拦截器未激活"
            
            # 测试事件恢复
            await self.form_engine._restore_time_field_events(self.page)
            
            final_status = await self.page.evaluate("""
                () => {
                    return window.timeFieldInterceptor ? {
                        isActive: window.timeFieldInterceptor.isActive
                    } : { isActive: null };
                }
            """)
            
            assert not final_status['isActive'], "拦截器未正确恢复"
            
            logger.info("✅ 时间字段事件拦截器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 时间字段事件拦截器测试失败: {e}")
            return False
    
    async def test_time_field_filling(self):
        """测试时间字段填写功能"""
        logger.info("🧪 测试时间字段填写功能...")
        
        try:
            # 准备测试数据（模拟Excel行数据）
            test_row = [''] * 20
            test_row[8] = '10'   # 开始小时
            test_row[9] = '3'    # 开始分钟1
            test_row[10] = '0'   # 开始分钟2
            test_row[12] = '11'  # 结束小时
            test_row[13] = '3'   # 结束分钟1
            test_row[14] = '0'   # 结束分钟2
            
            # 首先选择保险类型（医療保险）
            await self.page.click('#inPopupInsuranceDivision02')
            await self.page.wait_for_timeout(1000)
            
            # 记录填写前的表单字段状态
            before_status = await self._get_form_fields_status()
            logger.info(f"📊 填写前表单状态: {before_status}")
            
            # 执行时间字段填写
            await self.form_engine._fill_time_info_batch(test_row)
            
            # 等待一下确保所有操作完成
            await self.page.wait_for_timeout(2000)
            
            # 记录填写后的表单字段状态
            after_status = await self._get_form_fields_status()
            logger.info(f"📊 填写后表单状态: {after_status}")
            
            # 验证时间字段是否填写成功
            time_values = await self._get_time_field_values()
            logger.info(f"⏰ 时间字段值: {time_values}")
            
            # 验证其他表单字段是否仍然可用
            insurance_fields_ok = await self._verify_insurance_fields_enabled()
            
            # 测试结果评估
            success_criteria = [
                time_values['startHour'] == '10',
                time_values['startMinute1'] == '3', 
                time_values['startMinute2'] == '0',
                insurance_fields_ok,
                after_status['enabledRatio'] > 0.7
            ]
            
            success_count = sum(success_criteria)
            total_criteria = len(success_criteria)
            
            logger.info(f"📈 测试结果: {success_count}/{total_criteria} 项通过")
            
            if success_count >= total_criteria * 0.8:  # 80%通过率
                logger.info("✅ 时间字段填写功能测试通过")
                return True
            else:
                logger.warning("⚠️ 时间字段填写功能测试部分失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 时间字段填写功能测试失败: {e}")
            return False
    
    async def _get_form_fields_status(self):
        """获取表单字段状态"""
        return await self.page.evaluate("""
            () => {
                const allFields = document.querySelectorAll('#registModal input, #registModal select');
                let enabledCount = 0;
                let totalCount = 0;
                
                allFields.forEach(field => {
                    if (field.id && field.id.includes('inPopup')) {
                        totalCount++;
                        if (!field.disabled) {
                            enabledCount++;
                        }
                    }
                });
                
                return {
                    enabledCount: enabledCount,
                    totalCount: totalCount,
                    enabledRatio: totalCount > 0 ? enabledCount / totalCount : 0
                };
            }
        """)
    
    async def _get_time_field_values(self):
        """获取时间字段的值"""
        return await self.page.evaluate("""
            () => {
                return {
                    startHour: document.querySelector('#inPopupStartHour')?.value || '',
                    startMinute1: document.querySelector('#inPopupStartMinute1')?.value || '',
                    startMinute2: document.querySelector('#inPopupStartMinute2')?.value || '',
                    endHour: document.querySelector('#inPopupEndHour')?.value || '',
                    endMinute1: document.querySelector('#inPopupEndMinute1')?.value || '',
                    endMinute2: document.querySelector('#inPopupEndMinute2')?.value || ''
                };
            }
        """)
    
    async def _verify_insurance_fields_enabled(self):
        """验证保险字段是否启用"""
        return await self.page.evaluate("""
            () => {
                const insuranceFields = [
                    '#inPopupInsuranceDivision01',
                    '#inPopupInsuranceDivision02', 
                    '#inPopupInsuranceDivision03'
                ];
                
                return insuranceFields.some(selector => {
                    const field = document.querySelector(selector);
                    return field && !field.disabled;
                });
            }
        """)
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行时间字段修复测试...")
        
        try:
            # 初始化
            await self.setup()
            
            # 导航到表单
            if not await self.navigate_to_form():
                return False
            
            # 运行测试
            test_results = []
            
            # 测试1: 事件拦截器
            test_results.append(await self.test_time_field_interceptor())
            
            # 测试2: 时间字段填写
            test_results.append(await self.test_time_field_filling())
            
            # 汇总结果
            passed_tests = sum(test_results)
            total_tests = len(test_results)
            
            logger.info(f"📊 === 测试结果汇总 ===")
            logger.info(f"✅ 通过测试: {passed_tests}/{total_tests}")
            logger.info(f"📈 成功率: {passed_tests/total_tests*100:.1f}%")
            
            if passed_tests == total_tests:
                logger.info("🎉 所有测试通过！时间字段修复成功！")
                return True
            else:
                logger.warning("⚠️ 部分测试失败，需要进一步调试")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
        finally:
            # 清理
            try:
                await browser_manager.shutdown()
            except:
                pass


async def main():
    """主函数"""
    tester = TimeFieldFixTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("✅ 时间字段修复验证成功")
        sys.exit(0)
    else:
        logger.error("❌ 时间字段修复验证失败")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
