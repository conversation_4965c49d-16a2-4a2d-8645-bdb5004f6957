#!/usr/bin/env python3
"""
新规追加按钮数据登录窗口保护测试脚本
验证修复后的代码流程：检测お知らせ → 关闭お知らせ → 点击新规追加 → 保持数据登录窗口显示
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine


class AddButtonProtectionTest:
    """新规追加按钮数据登录窗口保护测试器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None
        
    async def run_test(self):
        """运行保护测试"""
        logger.info("🧪 开始新规追加按钮数据登录窗口保护测试")
        
        try:
            # 1. 初始化组件
            await self._initialize_components()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到测试页面
            await self._navigate_to_test_page()
            
            # 4. 测试完整的保护流程
            await self._test_protection_flow()
            
            logger.info("✅ 保护测试完成")
            
        except Exception as e:
            logger.error(f"❌ 保护测试失败: {e}", exc_info=True)
            raise
        finally:
            # 保持浏览器打开以便观察
            logger.info("🔍 浏览器将保持打开状态，请手动验证数据登录窗口是否持续显示")
            await asyncio.sleep(60)  # 等待60秒观察
            await browser_manager.close()
    
    async def _initialize_components(self):
        """初始化组件"""
        logger.info("🔧 初始化测试组件...")
        
        # 启动浏览器
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        
        # 初始化MCP备份工具
        await self.selector_executor.initialize_mcp_fallback()
        
        # 创建表单引擎
        class SimplePerformanceMonitor:
            def record_processed(self): pass
            def record_failed(self): pass
        
        self.form_engine = TennkiFormEngine(
            self.selector_executor, 
            SimplePerformanceMonitor()
        )
        
        logger.info("✅ 组件初始化完成")
    
    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔑 执行Kaipoke登录...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")
    
    async def _navigate_to_test_page(self):
        """导航到测试页面"""
        logger.info("🧭 导航到测试页面...")
        
        # 这里可以添加具体的导航逻辑
        # 暂时跳过，假设已经在正确页面
        logger.info("✅ 页面导航完成")
    
    async def _test_protection_flow(self):
        """测试保护流程"""
        logger.info("🛡️ 开始测试数据登录窗口保护流程...")
        
        # 步骤1：检测初始状态
        await self._check_initial_state()
        
        # 步骤2：测试お知らせ通知窗口处理
        await self._test_oshirase_handling()
        
        # 步骤3：测试新规追加按钮点击
        await self._test_add_button_click()
        
        # 步骤4：验证数据登录窗口保护
        await self._verify_form_protection()
        
        # 步骤5：多次验证窗口持久性
        await self._verify_form_persistence()
    
    async def _check_initial_state(self):
        """检查初始状态"""
        logger.info("📊 检查初始页面状态...")
        
        try:
            # 检查是否有お知らせ通知窗口
            oshirase_count = await self.page.locator('._icon-close__bF1y_').count()
            logger.info(f"   - お知らせ通知窗口数量: {oshirase_count}")
            
            # 检查是否有数据登录表单
            form_count = await self.page.locator('#registModal').count()
            logger.info(f"   - 数据登录表单数量: {form_count}")
            
            # 检查新规追加按钮
            add_button_count = await self.page.locator('#btn_area .cf:nth-child(1) :nth-child(1)').count()
            logger.info(f"   - 新规追加按钮数量: {add_button_count}")
            
        except Exception as e:
            logger.warning(f"⚠️ 初始状态检查失败: {e}")
    
    async def _test_oshirase_handling(self):
        """测试お知らせ通知窗口处理"""
        logger.info("📢 测试お知らせ通知窗口处理...")
        
        try:
            await self.form_engine._close_oshirase_notification_only(self.page)
            logger.info("✅ お知らせ通知窗口处理完成")
        except Exception as e:
            logger.warning(f"⚠️ お知らせ通知窗口处理失败: {e}")
    
    async def _test_add_button_click(self):
        """测试新规追加按钮点击"""
        logger.info("🔘 测试新规追加按钮点击...")
        
        try:
            # 使用简化的新规追加按钮点击流程
            await self.form_engine._simple_click_add_button(self.page)
            logger.info("✅ 新规追加按钮点击完成")
        except Exception as e:
            logger.error(f"❌ 新规追加按钮点击失败: {e}")
            raise
    
    async def _verify_form_protection(self):
        """验证数据登录窗口保护"""
        logger.info("🛡️ 验证数据登录窗口保护...")
        
        try:
            # 等待一段时间确保表单稳定
            await asyncio.sleep(3)
            
            # 检查数据登录表单是否存在且可见
            form_count = await self.page.locator('#registModal').count()
            form_visible = False
            
            if form_count > 0:
                form_visible = await self.page.locator('#registModal').is_visible()
            
            # 检查保险选择器是否可见
            insurance_count = await self.page.locator('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').count()
            insurance_visible = False
            
            if insurance_count > 0:
                insurance_visible = await self.page.locator('#inPopupInsuranceDivision01').is_visible() or \
                                  await self.page.locator('#inPopupInsuranceDivision02').is_visible()
            
            # 报告结果
            logger.info(f"📊 数据登录窗口保护验证结果:")
            logger.info(f"   - 表单模态框数量: {form_count}")
            logger.info(f"   - 表单模态框可见: {form_visible}")
            logger.info(f"   - 保险选择器数量: {insurance_count}")
            logger.info(f"   - 保险选择器可见: {insurance_visible}")
            
            if form_count > 0 and form_visible and insurance_count > 0 and insurance_visible:
                logger.info("✅ 数据登录窗口保护成功：表单正常显示且可操作")
                return True
            else:
                logger.error("❌ 数据登录窗口保护失败：表单未正常显示")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据登录窗口保护验证失败: {e}")
            return False
    
    async def _verify_form_persistence(self):
        """验证表单持久性"""
        logger.info("⏰ 验证表单持久性...")
        
        for i in range(5):
            await asyncio.sleep(2)
            logger.info(f"🔍 第 {i+1} 次持久性检查...")
            
            success = await self._verify_form_protection()
            if not success:
                logger.warning(f"⚠️ 第 {i+1} 次检查失败")
            else:
                logger.info(f"✅ 第 {i+1} 次检查通过")


async def main():
    """主函数"""
    tester = AddButtonProtectionTest()
    await tester.run_test()


if __name__ == "__main__":
    print("=" * 80)
    print("🧪 新规追加按钮数据登录窗口保护测试")
    print("=" * 80)
    print("测试流程：")
    print("1. ✅ 检测お知らせ通知窗口")
    print("2. ✅ 关闭お知らせ通知窗口")
    print("3. ✅ 点击新规追加按钮")
    print("4. ✅ 验证数据登录窗口保持显示")
    print("5. ✅ 验证保险选择器可见且可操作")
    print("=" * 80)
    
    try:
        asyncio.run(main())
        print("\n🎉 测试完成！请查看日志了解详细结果。")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
