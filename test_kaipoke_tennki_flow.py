#!/usr/bin/env python3
"""
Kaipoke Tennki工作流执行顺序测试脚本
用于验证：选择医疗保险 → 填写数据 → 点击登录按钮 → 继续下一条数据的完整流程
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine
from core.rpa_tools.tennki_data_processor import TennkiDataProcessor


class KaipokeTennkiFlowTester:
    """Kaipoke Tennki流程测试器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None
        
    async def run_test(self):
        """运行完整的流程测试"""
        logger.info("🧪 开始Kaipoke Tennki执行顺序测试")
        
        try:
            # 1. 初始化浏览器和组件
            await self._initialize_components()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到测试页面
            await self._navigate_to_test_page()
            
            # 4. 测试单条记录的完整流程
            await self._test_single_record_flow()
            
            logger.info("✅ 流程测试完成")
            
        except Exception as e:
            logger.error(f"❌ 流程测试失败: {e}", exc_info=True)
            raise
        finally:
            await browser_manager.close()
    
    async def _initialize_components(self):
        """初始化组件"""
        logger.info("🔧 初始化测试组件...")
        
        # 启动浏览器
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        
        # 初始化MCP备份工具
        await self.selector_executor.initialize_mcp_fallback()
        
        # 创建表单引擎（使用简单的性能监控器）
        class SimplePerformanceMonitor:
            def record_processed(self): pass
            def record_failed(self): pass
        
        self.form_engine = TennkiFormEngine(
            self.selector_executor, 
            SimplePerformanceMonitor()
        )
        
        logger.info("✅ 组件初始化完成")
    
    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔑 执行Kaipoke登录...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")
    
    async def _navigate_to_test_page(self):
        """导航到测试页面"""
        logger.info("🧭 导航到訪問看護页面...")
        
        # 这里可以添加具体的导航逻辑
        # 暂时跳过，假设已经在正确页面
        logger.info("✅ 页面导航完成")
    
    async def _test_single_record_flow(self):
        """测试单条记录的完整流程"""
        logger.info("📝 开始测试单条记录流程...")
        
        # 创建测试数据（模拟医疗保险记录）
        test_record = {
            'raw_data': [
                '', '', '', '', '', '', '', '',  # 前8列
                '10', '30', '00',  # 开始时间：10:30:00
                '', 
                '11', '30', '00',  # 结束时间：11:30:00
                '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                'Ⅰ',  # 估算字段
                '', '', '', '', '', '', '', '', '', '', '', '', '', ''
            ],
            'row_index': 999,  # 测试行号
            'insurance_type': '医療保险'
        }
        
        logger.info("🏥 测试流程：选择医疗保险 → 填写数据 → 点击登录按钮")
        
        try:
            # 执行完整的记录处理流程
            await self.form_engine._process_single_record(test_record, '医療保险')
            
            logger.info("✅ 单条记录流程测试成功")
            
            # 验证执行顺序
            await self._verify_execution_order()
            
        except Exception as e:
            logger.error(f"❌ 单条记录流程测试失败: {e}")
            raise
    
    async def _verify_execution_order(self):
        """验证执行顺序"""
        logger.info("🔍 验证执行顺序...")
        
        # 检查页面状态，确认是否按预期执行
        try:
            # 检查是否有新的数据登录窗口（表示已提交并准备下一条）
            modal_count = await self.page.locator('#registModal').count()
            
            if modal_count > 0:
                logger.info("✅ 检测到数据登录窗口，流程执行正确")
            else:
                logger.warning("⚠️ 未检测到数据登录窗口，可能流程未完全执行")
                
        except Exception as e:
            logger.debug(f"验证过程中的异常: {e}")
        
        logger.info("✅ 执行顺序验证完成")


async def main():
    """主函数"""
    tester = KaipokeTennkiFlowTester()
    await tester.run_test()


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 Kaipoke Tennki执行顺序测试")
    print("=" * 60)
    print("测试目标：")
    print("1. ✅ 选择医疗保险")
    print("2. ✅ 填写数据（包括时间字段强制激活）")
    print("3. ✅ 点击登录按钮提交")
    print("4. ✅ 继续下一条数据")
    print("=" * 60)
    
    try:
        asyncio.run(main())
        print("\n🎉 测试完成！请查看日志了解详细执行情况。")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
