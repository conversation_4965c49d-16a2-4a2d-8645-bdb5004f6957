#!/usr/bin/env python3
"""
新规追加按钮表单持久性测试脚本
测试点击新规追加按钮后，登录窗口是否会立即消失
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine


class AddButtonFormPersistenceTest:
    """新规追加按钮表单持久性测试器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None
        
    async def run_test(self):
        """运行测试"""
        logger.info("🧪 开始新规追加按钮表单持久性测试")
        
        try:
            # 1. 初始化组件
            await self._initialize_components()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到测试页面
            await self._navigate_to_test_page()
            
            # 4. 测试新规追加按钮点击
            await self._test_add_button_click()
            
            logger.info("✅ 测试完成")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}", exc_info=True)
            raise
        finally:
            # 保持浏览器打开以便观察
            logger.info("🔍 浏览器将保持打开状态，请手动检查表单是否持续显示")
            await asyncio.sleep(30)  # 等待30秒观察
            await browser_manager.close()
    
    async def _initialize_components(self):
        """初始化组件"""
        logger.info("🔧 初始化测试组件...")
        
        # 启动浏览器
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        
        # 初始化MCP备份工具
        await self.selector_executor.initialize_mcp_fallback()
        
        # 创建表单引擎
        class SimplePerformanceMonitor:
            def record_processed(self): pass
            def record_failed(self): pass
        
        self.form_engine = TennkiFormEngine(
            self.selector_executor, 
            SimplePerformanceMonitor()
        )
        
        logger.info("✅ 组件初始化完成")
    
    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔑 执行Kaipoke登录...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")
    
    async def _navigate_to_test_page(self):
        """导航到测试页面"""
        logger.info("🧭 导航到测试页面...")
        
        # 这里可以添加具体的导航逻辑
        # 暂时跳过，假设已经在正确页面
        logger.info("✅ 页面导航完成")
    
    async def _test_add_button_click(self):
        """测试新规追加按钮点击"""
        logger.info("🔘 开始测试新规追加按钮点击...")
        
        # 1. 检查初始状态
        initial_modal_count = await self.page.locator('#registModal').count()
        logger.info(f"📊 初始模态框数量: {initial_modal_count}")
        
        # 2. 点击新规追加按钮
        logger.info("🔘 点击新规追加按钮...")
        try:
            await self.form_engine._click_add_button()
            logger.info("✅ 新规追加按钮点击完成")
        except Exception as e:
            logger.error(f"❌ 新规追加按钮点击失败: {e}")
            raise
        
        # 3. 等待一段时间观察表单状态
        logger.info("⏳ 等待5秒观察表单状态...")
        await asyncio.sleep(5)
        
        # 4. 检查表单是否仍然存在
        await self._check_form_persistence()
        
        # 5. 多次检查表单状态
        for i in range(3):
            await asyncio.sleep(2)
            logger.info(f"🔍 第 {i+1} 次检查表单状态...")
            await self._check_form_persistence()
    
    async def _check_form_persistence(self):
        """检查表单持久性"""
        try:
            # 检查模态框是否存在
            modal_count = await self.page.locator('#registModal').count()
            modal_visible = False
            
            if modal_count > 0:
                modal_visible = await self.page.locator('#registModal').is_visible()
            
            # 检查保险选择器是否存在
            insurance_selector_count = await self.page.locator('#inPopupInsuranceDivision01, #inPopupInsuranceDivision02').count()
            
            # 检查表单字段是否可见
            form_fields_visible = False
            if insurance_selector_count > 0:
                form_fields_visible = await self.page.locator('#inPopupInsuranceDivision01').is_visible() or \
                                    await self.page.locator('#inPopupInsuranceDivision02').is_visible()
            
            # 报告状态
            logger.info(f"📊 表单状态检查结果:")
            logger.info(f"   - 模态框数量: {modal_count}")
            logger.info(f"   - 模态框可见: {modal_visible}")
            logger.info(f"   - 保险选择器数量: {insurance_selector_count}")
            logger.info(f"   - 表单字段可见: {form_fields_visible}")
            
            if modal_count > 0 and modal_visible and insurance_selector_count > 0 and form_fields_visible:
                logger.info("✅ 表单持久性测试通过：登录窗口正常显示")
                return True
            else:
                logger.warning("⚠️ 表单持久性测试失败：登录窗口可能已消失")
                return False
                
        except Exception as e:
            logger.error(f"❌ 表单状态检查失败: {e}")
            return False


async def main():
    """主函数"""
    tester = AddButtonFormPersistenceTest()
    await tester.run_test()


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 新规追加按钮表单持久性测试")
    print("=" * 60)
    print("测试目标：")
    print("1. ✅ 点击新规追加按钮")
    print("2. ✅ 验证登录窗口出现")
    print("3. ✅ 确认登录窗口不会立即消失")
    print("4. ✅ 检查保险选择器可见性")
    print("=" * 60)
    
    try:
        asyncio.run(main())
        print("\n🎉 测试完成！请查看日志了解详细结果。")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
