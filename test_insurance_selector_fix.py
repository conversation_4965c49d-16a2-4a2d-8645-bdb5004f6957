#!/usr/bin/env python3
"""
保险选择器disabled问题修复验证脚本
测试超强激活保险选择器功能
"""

import sys
import os

def check_insurance_selector_fixes():
    """检查保险选择器修复"""
    print("🔧 检查保险选择器修复...")
    
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    if not os.path.exists(engine_file):
        print(f"❌ 文件不存在: {engine_file}")
        return False
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新增的修复功能
    fixes_checks = [
        ("_super_activate_insurance_selector", "超强激活保险选择器函数"),
        ("超强激活保险选择器（解决disabled问题）", "disabled问题解决方案"),
        ("强制移除所有禁用属性", "禁用属性移除"),
        ("确保可见性和可交互性", "可见性确保"),
        ("强制触发表单初始化函数", "表单初始化"),
        ("多重保险选择策略（增强版 - 解决disabled问题）", "增强版选择策略"),
        ("首先执行超强激活", "激活优先策略"),
        ("策略2: 强制点击", "强制点击策略"),
        ("策略3: JavaScript点击", "JavaScript点击策略")
    ]
    
    all_passed = True
    for check_text, description in fixes_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def check_activation_strategies():
    """检查激活策略"""
    print("\n🎯 检查激活策略...")
    
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查激活策略
    strategy_checks = [
        ("removeAttribute('disabled')", "disabled属性移除"),
        ("radio.disabled = false", "disabled状态重置"),
        ("pointerEvents = 'auto'", "指针事件启用"),
        ("initializeForm", "表单初始化调用"),
        ("changeDivision", "保险切换函数调用"),
        ("enableInsuranceOptions", "保险选项启用"),
        ("click(force=True)", "强制点击"),
        ("dispatchEvent(new Event('change'", "change事件触发"),
        ("dispatchEvent(new Event('click'", "click事件触发")
    ]
    
    all_passed = True
    for check_text, description in strategy_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def check_error_handling_improvements():
    """检查错误处理改进"""
    print("\n🛡️ 检查错误处理改进...")
    
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查错误处理改进
    error_handling_checks = [
        ("标准点击成功", "标准点击成功处理"),
        ("标准点击失败", "标准点击失败处理"),
        ("强制点击成功", "强制点击成功处理"),
        ("强制点击失败", "强制点击失败处理"),
        ("JavaScript点击完成", "JavaScript点击处理"),
        ("超强激活结果", "激活结果记录"),
        ("超强激活失败", "激活失败处理")
    ]
    
    all_passed = True
    for check_text, description in error_handling_checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def show_disabled_problem_solution():
    """显示disabled问题解决方案"""
    print("\n📋 保险选择器disabled问题解决方案")
    print("=" * 50)
    
    print("🔍 问题诊断:")
    print("   - 日志显示: element is not enabled")
    print("   - 元素状态: <input disabled ...>")
    print("   - 根本原因: 保险选择器本身被禁用")
    
    print("\n🔧 解决策略:")
    print("1. 超强激活保险选择器")
    print("   - 强制移除disabled属性")
    print("   - 重置disabled状态为false")
    print("   - 确保可见性和可交互性")
    print("   - 激活父容器")
    
    print("\n2. 多函数调用激活")
    print("   - initializeForm()")
    print("   - changeDivision()")
    print("   - enableInsuranceOptions()")
    print("   - activateForm()")
    print("   - setupForm()")
    
    print("\n3. 多重点击策略")
    print("   - 策略1: 标准点击")
    print("   - 策略2: 强制点击 (force=True)")
    print("   - 策略3: JavaScript直接操作")
    
    print("\n4. 事件触发确保")
    print("   - change事件触发")
    print("   - click事件触发")
    print("   - 状态验证")

def generate_fix_summary():
    """生成修复总结"""
    print("\n📊 保险选择器disabled问题修复验证")
    print("=" * 50)
    
    fixes_check = check_insurance_selector_fixes()
    strategy_check = check_activation_strategies()
    error_check = check_error_handling_improvements()
    
    print(f"\n📈 检查结果:")
    print(f"   - 修复功能: {'✅ 通过' if fixes_check else '❌ 失败'}")
    print(f"   - 激活策略: {'✅ 通过' if strategy_check else '❌ 失败'}")
    print(f"   - 错误处理: {'✅ 通过' if error_check else '❌ 失败'}")
    
    if fixes_check and strategy_check and error_check:
        print("\n🎉 保险选择器disabled问题修复已正确实施！")
        print("\n📝 预期效果:")
        print("   1. 解决保险选择器disabled状态问题")
        print("   2. 强制激活所有相关表单元素")
        print("   3. 多重点击策略确保选择成功")
        print("   4. 详细的激活过程日志")
        
        print("\n🚀 解决 '选择器一直未激活' 问题:")
        print("   1. 超强激活会强制移除disabled属性")
        print("   2. 多函数调用会触发表单初始化")
        print("   3. 多重点击策略会确保选择成功")
        print("   4. 事件触发会激活后续字段")
        
        print("\n📋 下一步测试建议:")
        print("   1. 运行kaipoke_tennki工作流")
        print("   2. 观察保险选择器激活过程")
        print("   3. 检查disabled属性是否被移除")
        print("   4. 确认后续字段是否正常激活")
        return True
    else:
        print("\n⚠️ 部分修复可能未正确实施，请检查代码")
        return False

if __name__ == "__main__":
    print("🔧 保险选择器disabled问题修复验证")
    print("=" * 60)
    
    # 显示问题解决方案
    show_disabled_problem_solution()
    
    # 执行检查
    success = generate_fix_summary()
    
    if success:
        print(f"\n✅ 保险选择器修复验证完成 - 所有检查通过")
        print(f"\n💡 这应该能解决 '选择器一直未激活' 的问题！")
        sys.exit(0)
    else:
        print(f"\n❌ 保险选择器修复验证失败 - 请检查实施")
        sys.exit(1)
